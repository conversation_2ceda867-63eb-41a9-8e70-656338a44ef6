#!/usr/bin/env python3
"""
Debug script to help identify parsing issues with transaction amounts.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from expense_info import ExpenseInfo
import re

def debug_transaction_parsing(transaction_text):
    """Debug what the parser is extracting from a transaction."""
    
    print("=" * 80)
    print("TRANSACTION PARSING DEBUG")
    print("=" * 80)
    print(f"Input text: {transaction_text}")
    print("-" * 80)
    
    # Show what the amount pattern finds
    amount_pattern = r"(\$?\s?[\d.,]+)"
    all_amounts = re.findall(amount_pattern, transaction_text)
    print(f"All amounts found by regex: {all_amounts}")
    
    if all_amounts:
        raw_amount = all_amounts[0]
        print(f"First amount selected: '{raw_amount}'")
        
        # Show normalization process
        normalized = ExpenseInfo._normalize_amount(raw_amount)
        print(f"Normalized amount: '{normalized}'")
    
    print("-" * 80)
    
    # Show full parsing result
    result = ExpenseInfo.parse(transaction_text)
    if result:
        print(f"✅ Parsing successful:")
        print(f"   Place: {result.place}")
        print(f"   Amount: {result.amount}")
        print(f"   Currency: {result.currency}")
    else:
        print("❌ Parsing failed or message was filtered")
    
    print("=" * 80)

if __name__ == "__main__":
    # Test with some example transactions
    test_transactions = [
        "Compra por $120,000 COP en Supermercado XYZ",
        "Scotiabank Colpatria: Realizaste transaccion en DLO*GOOGLE TIDAL por 17,500",
        "Netflix subscription $15.99 USD",
        "Transferencia por $90,000 desde cta *9534",
    ]
    
    for transaction in test_transactions:
        debug_transaction_parsing(transaction)
        print("\n")
    
    print("To debug a specific transaction, run:")
    print("python3 debug_parsing.py")
    print("Then call: debug_transaction_parsing('your transaction text here')")
