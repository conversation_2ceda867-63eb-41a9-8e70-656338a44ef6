import logging
from datetime import datetime
import requests

from expense_info import ExpenseInfo
from google_sheets_client import GoogleSheetsClient
from expense_categorizer import ExpenseCategorizer

class CurrencyConverter:
    """
    Clase utilitaria para obtener la tasa de cambio actual de USD a COP utilizando múltiples servicios.
    """
    @staticmethod
    def fetch_usd_to_cop_rate():
        # Lista de funciones de servicios en orden de prioridad
        services = [
            CurrencyConverter._fetch_from_exchangerate_api,
            CurrencyConverter._fetch_from_freecurrencyapi,
            CurrencyConverter._fetch_from_exchangeratesapi_io
        ]

        for service in services:
            try:
                rate = service()
                if rate:
                    return rate
            except Exception as e:
                logging.warning(f"Error al obtener la tasa de {service.__name__}: {e}")

        # Si todas las llamadas fallan, devolver un valor por defecto
        logging.error("No se pudo obtener la tasa de cambio USD a COP de ningún servicio.")
        return 4000.0

    @staticmethod
    def _fetch_from_exchangerate_api():
        """
        Obtiene la tasa de cambio desde ExchangeRate-API (sin clave de API).
        """
        response = requests.get("https://open.er-api.com/v6/latest/USD")
        response.raise_for_status()
        data = response.json()
        return float(data['rates']['COP'])

    @staticmethod
    def _fetch_from_freecurrencyapi():
        """
        Obtiene la tasa de cambio desde Freecurrencyapi.com (requiere clave de API gratuita).
        """
        api_key = 'TU_CLAVE_DE_API'
        response = requests.get(f"https://api.freecurrencyapi.com/v1/latest?apikey={api_key}&currencies=COP")
        response.raise_for_status()
        data = response.json()
        return float(data['data']['COP'])

    @staticmethod
    def _fetch_from_exchangeratesapi_io():
        """
        Obtiene la tasa de cambio desde ExchangeRatesAPI.io (requiere clave de API gratuita).
        """
        api_key = 'TU_CLAVE_DE_API'
        response = requests.get(f"https://api.exchangeratesapi.io/v1/latest?access_key={api_key}&symbols=COP")
        response.raise_for_status()
        data = response.json()
        return float(data['rates']['COP'])

class PushNotificationProcessor:
    """
    Orchestrates the workflow of taking a push notification input
    (app_name, title, description), parsing the expense, converting currencies,
    and logging it to Google Sheets via the GoogleSheetsClient.
    """

    def __init__(self, sheets_client: GoogleSheetsClient):
        """
        :param sheets_client: An instance of GoogleSheetsClient.
        """
        self.sheets_client = sheets_client
        self.categorizer = ExpenseCategorizer()

    def process_push_notification(self, app_name, title, description):
        """
        Main method to process a push notification:
         1. Parse monetary values from (title + description) using ExpenseInfo.
         2. Map currency amounts to the correct columns (COP -> col B, USD -> col C).
         3. Automatically categorize the expense into main category and subcategory.
         4. Append the row to the Google Sheet with conversion and categorization.
        """
        combined_text = f"{title} {description}"
        parsed_info = ExpenseInfo.parse(combined_text)

        if parsed_info is None:
            logging.warning(f"Could not parse push notification text: '{combined_text}'")
            return

        usd_to_cop = CurrencyConverter.fetch_usd_to_cop_rate()

        cop_val = 0.0
        usd_val = 0.0
        if parsed_info.currency == "COP":
            cop_val = float(parsed_info.amount)
            usd_val = round(cop_val / usd_to_cop, 4)
        elif parsed_info.currency == "USD":
            usd_val = float(parsed_info.amount)
            cop_val = round(usd_val * usd_to_cop)
        else:
            cop_val = float(parsed_info.amount)
            usd_val = round(cop_val / usd_to_cop, 4)

        current_dt = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Categorize the expense
        main_category, subcategory = self.categorizer.categorize_expense(
            place=parsed_info.place,
            amount=parsed_info.amount,
            description=combined_text
        )

        row = [
            current_dt,           # Date (Column A)
            usd_to_cop,          # Exchange Rate (Column B)
            cop_val,             # COP Amount (Column C)
            usd_val,             # USD Amount (Column D)
            app_name,            # App (Column E)
            description.strip(), # Description (Column F)
            main_category,       # Categoría Principal (Column G)
            subcategory          # Subcategoría (Column H)
        ]

        # Append transaction row
        self.sheets_client.append_row(row)

        logging.info(f"Expense categorized as: {main_category} -> {subcategory}")
