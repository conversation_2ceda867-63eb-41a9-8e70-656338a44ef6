from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials

import logging

class GoogleSheetsClient:
    """
    Handles all interactions with the Google Sheets API:
      - Authenticates via the service account JSON.
      - Appends rows to the specified sheet/tab.
    """

    def __init__(self, spreadsheet_id, credentials_file, sheet_range="ALL!A:I"):
        """
        :param spreadsheet_id: The ID of the target Google Spreadsheet.
        :param credentials_file: Path to the service account credentials JSON.
        :param sheet_range: The sheet/tab and columns to append data (e.g. 'ALL!A:I').
                           Now includes columns for Date, Exchange Rate, COP, USD, App, Description,
                           Main Category, and Subcategory.
        """
        self.spreadsheet_id = spreadsheet_id
        self.credentials_file = credentials_file
        self.sheet_range = sheet_range
        self.service = self._init_service()

        # Configure logging (if not configured elsewhere)
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s"
        )

    def _init_service(self):
        """
        Builds and returns a Google Sheets service object.
        """
        scopes = ["https://www.googleapis.com/auth/spreadsheets"]
        credentials = Credentials.from_service_account_file(
            self.credentials_file,
            scopes=scopes
        )
        try:
            service = build("sheets", "v4", credentials=credentials)
            logging.info("Google Sheets service initialized successfully.")
            return service
        except Exception as e:
            logging.error(f"Error creating Google Sheets service: {e}")
            raise e

    def append_row(self, row_values):
        """
        Appends a single row to the configured sheet/tab.

        :param row_values: A list representing a single row of data.
        """
        if not isinstance(row_values, list):
            raise ValueError("row_values must be a list.")

        sheet = self.service.spreadsheets()
        body = {"values": [row_values]}  # single row

        try:
            request = sheet.values().append(
                spreadsheetId=self.spreadsheet_id,
                range=self.sheet_range,
                valueInputOption="RAW",
                body=body
            )
            response = request.execute()
            logging.info(f"Data appended to {self.sheet_range}: {response}")
        except Exception as e:
            logging.error(f"Error appending data to the Google Sheet: {e}")
