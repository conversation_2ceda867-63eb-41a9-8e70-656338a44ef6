from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials

import logging

class GoogleSheetsClient:
    """
    Handles all interactions with the Google Sheets API:
      - Authenticates via the service account JSON.
      - Appends rows to the specified sheet/tab.
    """

    def __init__(self, spreadsheet_id, credentials_file, sheet_range="ALL!A:I"):
        """
        :param spreadsheet_id: The ID of the target Google Spreadsheet.
        :param credentials_file: Path to the service account credentials JSON.
        :param sheet_range: The sheet/tab and columns to append data (e.g. 'ALL!A:I').
                           Now includes columns for Date, Exchange Rate, COP, USD, App, Description,
                           Main Category, and Subcategory.
        """
        self.spreadsheet_id = spreadsheet_id
        self.credentials_file = credentials_file
        self.sheet_range = sheet_range
        self.service = self._init_service()

        # Configure logging (if not configured elsewhere)
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s"
        )

    def _init_service(self):
        """
        Builds and returns a Google Sheets service object.
        """
        scopes = ["https://www.googleapis.com/auth/spreadsheets"]
        credentials = Credentials.from_service_account_file(
            self.credentials_file,
            scopes=scopes
        )
        try:
            service = build("sheets", "v4", credentials=credentials)
            logging.info("Google Sheets service initialized successfully.")
            return service
        except Exception as e:
            logging.error(f"Error creating Google Sheets service: {e}")
            raise e

    def append_row(self, row_values):
        """
        Appends a single row to the configured sheet/tab.

        :param row_values: A list representing a single row of data.
        """
        if not isinstance(row_values, list):
            raise ValueError("row_values must be a list.")

        sheet = self.service.spreadsheets()
        body = {"values": [row_values]}  # single row

        try:
            request = sheet.values().append(
                spreadsheetId=self.spreadsheet_id,
                range=self.sheet_range,
                valueInputOption="RAW",
                body=body
            )
            response = request.execute()
            logging.info(f"Data appended to {self.sheet_range}: {response}")
        except Exception as e:
            logging.error(f"Error appending data to the Google Sheet: {e}")

    def create_sheet_tab(self, sheet_name):
        """
        Creates a new sheet tab in the spreadsheet.

        :param sheet_name: Name of the new sheet tab to create.
        """
        try:
            # First check if sheet already exists
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()

            existing_sheets = [sheet['properties']['title'] for sheet in spreadsheet['sheets']]

            if sheet_name in existing_sheets:
                logging.info(f"Sheet '{sheet_name}' already exists, skipping creation")
                return

            # Create new sheet
            request_body = {
                'requests': [{
                    'addSheet': {
                        'properties': {
                            'title': sheet_name
                        }
                    }
                }]
            }

            response = self.service.spreadsheets().batchUpdate(
                spreadsheetId=self.spreadsheet_id,
                body=request_body
            ).execute()

            logging.info(f"Created new sheet tab: {sheet_name}")

        except Exception as e:
            logging.error(f"Error creating sheet tab '{sheet_name}': {e}")
            raise

    def batch_update_rows(self, data_rows):
        """
        Updates multiple rows at once using batch update.

        :param data_rows: List of lists, where each inner list represents a row.
        """
        if not data_rows:
            logging.warning("No data provided for batch update")
            return

        try:
            body = {"values": data_rows}

            # Clear existing data first
            self.service.spreadsheets().values().clear(
                spreadsheetId=self.spreadsheet_id,
                range=self.sheet_range
            ).execute()

            # Update with new data
            response = self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id,
                range=self.sheet_range,
                valueInputOption="RAW",
                body=body
            ).execute()

            logging.info(f"Batch updated {len(data_rows)} rows to {self.sheet_range}")

        except Exception as e:
            logging.error(f"Error in batch update: {e}")
            raise

    def get_sheet_data(self, range_name=None):
        """
        Retrieves data from the sheet.

        :param range_name: Optional specific range, defaults to configured sheet_range.
        :return: List of lists representing the sheet data.
        """
        range_to_use = range_name or self.sheet_range

        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range=range_to_use
            ).execute()

            values = result.get('values', [])
            logging.info(f"Retrieved {len(values)} rows from {range_to_use}")
            return values

        except Exception as e:
            logging.error(f"Error retrieving data from {range_to_use}: {e}")
            raise
