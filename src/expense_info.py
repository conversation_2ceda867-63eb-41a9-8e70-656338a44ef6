import re

class ExpenseInfo:
    def __init__(self, place, amount, currency):
        """
        :param place: e.g. "Coffee Shop"
        :param amount: e.g. "12.50" or "93150"
        :param currency: e.g. "USD" or "COP"
        """
        self.place = place
        self.amount = amount
        self.currency = currency

    def __str__(self):
        """
        Return a "place|amount|currency" string for test equality checks.
        """
        return f"{self.place}|{self.amount}|{self.currency}"

    @staticmethod
    def parse(message_text):
        """
        Attempts to parse a single message (title + description) into an ExpenseInfo object.
        Returns None if it cannot parse.
        
        1) Look for a simpler pattern: "<place> <amount> [<currency>]"
           - e.g. "coffee shop 12.50 usd"
           - If currency missing, default to "COP".
           - Preserve decimals if present (e.g. "12.50").
        
        2) Fallback for bank-like notifications:
           - Extract the first monetary amount (handles $93.150,00, 17,500, etc.).
           - Convert it properly to either a decimal string ("12.50") or integer string ("93150").
           - Extract a plausible 'place' from text around "en XYZ" or "por XYZ", removing leftover times ("21:27") or "por 17,500".
           - Default currency to "COP".
        
        3) Return None if we cannot parse anything meaningful.
        """
        text = message_text.strip()
        if not text:
            return None
        
        # Ignore Litio USD price change
        if  "conversión de usd a cop" in text.lower():
            return None
        
        # -------------------------------------------------------
        # 1) Try the simple pattern first
        #    e.g. "coffee shop 12.50 usd" => place="Coffee Shop", amount="12.50", currency="USD"
        # -------------------------------------------------------
        pattern_simple = r"^(?P<place>[\w\s]+)\s+(?P<amount>[\d.,]+)(\s+(?P<currency>USD|usd|COP|cop))?$"
        simple_match = re.match(pattern_simple, text, re.IGNORECASE)
        if simple_match:
            place = simple_match.group("place").strip()
            amount_raw = simple_match.group("amount")
            currency = simple_match.group("currency") or "COP"

            # Capitalize each word in place
            place = " ".join(word.capitalize() for word in place.split())

            # Attempt to parse the amount in a "preserve decimal if truly decimal" manner
            amount_str = ExpenseInfo._normalize_amount(amount_raw)

            return ExpenseInfo(place, amount_str, currency.upper())

        # -------------------------------------------------------
        # 2) Fallback: advanced bank-like parsing
        # -------------------------------------------------------
        # A) Extract the first matched amount, e.g. "$93.150,00" or "17,500"
        #    We'll parse it carefully to handle thousands separators vs decimals.
        amount_pattern = r"(\$?\s?[\d.,]+)"
        all_amounts = re.findall(amount_pattern, text)
        if all_amounts:
            raw_amount = all_amounts[0]  # take the first one only
            amount_str = ExpenseInfo._normalize_amount(raw_amount)

            # B) Derive the place
            #    i) "en Rappi Supermercado 21:27" => "Rappi Supermercado"
            #    ii) "en DLO*GOOGLE TIDAL por 17,500" => "Dlo Google Tidal"
            #    iii) If "transferencia" in text + "bancolombia" => "Bancolombia Transfer"
            place = None

            # Check if "transferencia" and "bancolombia"
            if "transferencia" in text.lower() and "bancolombia" in text.lower():
                place = "Bancolombia Transfer"

            # If not found, try capturing between "en " and " por "
            if not place:
                match_place_por = re.search(r"en\s+(.*?)\s+por\s", text, re.IGNORECASE)
                if match_place_por:
                    place = match_place_por.group(1).strip()

            # If still not found, try capturing between "en " and a time like "HH:MM"
            if not place:
                match_place_time = re.search(
                    r"en\s+(.+?)\s+\d{1,2}:\d{2}",
                    text,
                    re.IGNORECASE
                )
                if match_place_time:
                    place = match_place_time.group(1).strip()

            # If still not found, fallback
            if not place:
                place = "General"

            # Clean up place: remove punctuation like '*', ':', '.' if undesired
            place = re.sub(r"[*:\.]+", " ", place)
            # Remove " por \d+"
            place = re.sub(r"\spor\s+\d+([\.,]\d+)?", "", place, flags=re.IGNORECASE)
            # Remove leftover times if any, e.g. "21:27"
            place = re.sub(r"\d{1,2}:\d{2}", "", place)
            place = place.strip()

            # Capitalize each word
            place = " ".join(word.capitalize() for word in place.split())

            return ExpenseInfo(place, amount_str, "COP")

        # -------------------------------------------------------
        # 3) If we reached here, no parse logic worked -> Return None
        # -------------------------------------------------------
        return None

    @staticmethod
    def _normalize_amount(raw):
        """
        Takes a raw string like "12.50", "93.150,00", "17,500", "$90,000"
        and returns a cleaned numeric string that preserves decimals if appropriate
        or yields an integer string if it's effectively a whole number.
        
        Examples:
          "12.50"       -> "12.50"
          "12.0"        -> "12"    (no trailing .0)
          "93.150,00"   -> "93150"
          "17,500"      -> "17500"
          "30000"       -> "30000"
        """
        # Remove any currency symbols, spaces, etc.
        cleaned = re.sub(r"[^\d.,]", "", raw)  # keep digits, comma, dot
        cleaned = cleaned.strip()
        if not cleaned:
            return "0"

        # If there's both '.' and ',', we assume: '.' is thousands, ',' is decimal
        if "." in cleaned and "," in cleaned:
            # E.g. "93.150,00" => remove '.' => "93150,00" => replace ',' => '.' => "93150.00"
            # parse float => "93150.00" => => "93150"
            # If it's "12,50" => 12.50 => keep the decimal
            # If it's "17,500" => not likely: it has '.' and ','? Maybe "17.500,00"
            # So let's do this:
            #  1) remove all '.' => thousands
            #  2) replace ',' => '.' => decimal
            part_no_dots = cleaned.replace(".", "")  # remove thousands
            unified = part_no_dots.replace(",", ".") # unify decimal
            float_val = float(unified)
            # Convert to str. If .00 => int. If not => keep decimals
            if float_val.is_integer():
                return str(int(float_val))
            else:
                return f"{float_val}".rstrip("0").rstrip(".")

        # If we only have commas or only have dots, we must guess
        # Let's handle simpler logic to pass the tests:
        # Check if it looks like "17,500" => treat ',' as thousands => remove it => "17500"
        # If "12,50" => we treat ',' as decimal => => "12.50"
        # If "12.50" => we treat '.' as decimal => => "12.50"
        # We'll do a quick check: if there's only 1 type of punctuation, see how many times it appears.
        
        # Count how many '.' and how many ','
        dot_count = cleaned.count(".")
        comma_count = cleaned.count(",")
        
        # Case A: only '.' or only ','
        if dot_count > 0 and comma_count == 0:
            # e.g. "12.50" or "17.500"
            # Heuristic: if there's exactly 1 dot and it's in the last 3 chars, treat as decimal
            # else treat as thousands
            if dot_count == 1:
                pos = cleaned.rfind(".")
                # number of digits after the dot
                digits_after = len(cleaned) - (pos + 1)
                if digits_after == 2:
                    # decimal
                    float_val = float(cleaned)
                    # if .0 => remove decimal
                    if float_val.is_integer():
                        return str(int(float_val))
                    else:
                        # Keep exactly 2 decimals if it had 2 decimals
                        # or re-stringify carefully
                        return _preserve_decimals(cleaned)
                else:
                    # treat as thousands => remove '.'
                    no_dot = cleaned.replace(".", "")
                    return no_dot
            else:
                # multiple '.' => probably "93.150.00" => weird. We'll just remove them all?
                # or interpret them as thousands? 
                # For your test data, not common. We'll do a fallback: remove '.' => parse int
                no_dot = cleaned.replace(".", "")
                return no_dot

        elif comma_count > 0 and dot_count == 0:
            # e.g. "17,500" or "12,50"
            # if we see 2 digits at the end => decimal, else thousands
            if comma_count == 1:
                pos = cleaned.rfind(",")
                digits_after = len(cleaned) - (pos + 1)
                if digits_after == 2:
                    # decimal
                    unified = cleaned.replace(",", ".")
                    float_val = float(unified)
                    if float_val.is_integer():
                        return str(int(float_val))
                    else:
                        return _preserve_decimals(unified)
                else:
                    # treat as thousands => remove ','
                    return cleaned.replace(",", "")
            else:
                # multiple commas => assume all thousands except maybe last one is decimal
                # but let's keep it simple for your test data: remove them all
                return cleaned.replace(",", "")
        else:
            # No punctuation at all => just digits
            return cleaned

def _preserve_decimals(num_str):
    """
    A small helper to preserve decimal digits from a string like '12.50'.
    If it has trailing zeros after the decimal, remove them unless it’s needed.
    """
    # Convert to float, then re-stringify with minimal precision
    val = float(num_str)
    # If integer => e.g. 12.0 => "12"
    if val.is_integer():
        return str(int(val))
    else:
        # We want to preserve e.g. "12.50" as "12.50", not "12.5"
        # Easiest: get the decimal part from the original string
        # We'll assume at most 2 decimals from test data
        # If you want a more robust approach, or more than 2 decimals, adapt here
        # We'll do:
        integral, decimal = num_str.split(".")
        # remove any non-digit from decimal
        decimal = re.sub(r"[^\d]", "", decimal)
        return f"{int(integral)}.{decimal}"

