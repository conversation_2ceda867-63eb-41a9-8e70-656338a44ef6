import re

class MessageFilter:
    """
    Configurable message filtering system to exclude unwanted messages from processing.
    """

    def __init__(self):
        """Initialize the filter with predefined patterns for different message types."""
        self.currency_exchange_patterns = [
            r"tasa de recarga",
            r"conversión de usd a cop",
            r"tasa de cambio",
            r"exchange rate",
            r"tipo de cambio",
            r"\bcop\b.*\busd\b",
            r"\busd\b.*\bcop\b",
            r"precio.*dólar",
            r"cotización"
        ]

        self.credit_card_promotional_patterns = [
            r"scotiabank colpatria.*te ofrece",
            r"scotiabank colpatria.*promoción",
            r"scotiabank colpatria.*oferta",
            r"visa.*cashback",
            r"meta de \$\d+",
            r"cashback.*obtén",
            r"cashback.*acumula",
            r"promoción.*tarjeta",
            r"oferta.*crédito",
            r"beneficios.*visa",
            r"puntos.*recompensa",
            r"descuento.*compras",
            r"programa.*fidelidad",
            r"0%.*interés",
            r"sin.*intereses"
        ]

        self.fitness_promotional_patterns = [
            r"bodytech",
            r"reto.*fitness",
            r"plan anual.*gym",
            r"membresía.*gimnasio",
            r"entrenamiento.*personal",
            r"clase.*grupal",
            r"rutina.*ejercicio",
            r"desafío.*fitness"
        ]

        self.banking_security_patterns = [
            r"bancolombia.*contraseña",
            r"clave.*bloqueada",
            r"seguridad.*cuenta",
            r"novedad.*transacción",
            r"alerta.*seguridad",
            r"contacte.*\d{3}[-\s]?\d{3}[-\s]?\d{4}",
            r"problema.*acceso",
            r"verificación.*identidad",
            r"token.*vencido",
            r"actualice.*datos"
        ]

    def should_filter_message(self, message_text):
        """
        Determine if a message should be filtered out based on configured patterns.

        Args:
            message_text (str): The message text to evaluate

        Returns:
            tuple: (should_filter: bool, reason: str) - True if message should be filtered
        """
        if not message_text or not isinstance(message_text, str):
            return True, "Empty or invalid message"

        text_lower = message_text.lower().strip()

        # Check specific pattern categories first (these may not have numeric content)

        # Check currency exchange patterns
        for pattern in self.currency_exchange_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True, "Currency exchange notification"

        # Check credit card promotional patterns
        for pattern in self.credit_card_promotional_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True, "Credit card promotional message"

        # Check fitness promotional patterns
        for pattern in self.fitness_promotional_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True, "Fitness promotional message"

        # Check banking security patterns
        for pattern in self.banking_security_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True, "Banking security/notification message"

        # Check if message has no numeric content (after checking specific patterns)
        if not re.search(r'\d', text_lower):
            return True, "No numeric content"

        return False, "Message passed all filters"

    def add_custom_pattern(self, category, pattern):
        """
        Add a custom pattern to a specific category.

        Args:
            category (str): One of 'currency_exchange', 'credit_card_promotional',
                          'fitness_promotional', 'banking_security'
            pattern (str): Regular expression pattern to add
        """
        category_map = {
            'currency_exchange': self.currency_exchange_patterns,
            'credit_card_promotional': self.credit_card_promotional_patterns,
            'fitness_promotional': self.fitness_promotional_patterns,
            'banking_security': self.banking_security_patterns
        }

        if category in category_map:
            category_map[category].append(pattern)
        else:
            raise ValueError(f"Unknown category: {category}")

class ExpenseInfo:
    def __init__(self, place, amount, currency):
        """
        :param place: e.g. "Coffee Shop"
        :param amount: e.g. "12.50" or "93150"
        :param currency: e.g. "USD" or "COP"
        """
        self.place = place
        self.amount = amount
        self.currency = currency

    def __str__(self):
        """
        Return a "place|amount|currency" string for test equality checks.
        """
        return f"{self.place}|{self.amount}|{self.currency}"

    @staticmethod
    def parse(message_text):
        """
        Attempts to parse a single message (title + description) into an ExpenseInfo object.
        Returns None if it cannot parse or if the message should be filtered out.

        1) First applies message filtering to exclude unwanted message types
        2) Look for a simpler pattern: "<place> <amount> [<currency>]"
           - e.g. "coffee shop 12.50 usd"
           - If currency missing, default to "COP".
           - Preserve decimals if present (e.g. "12.50").

        3) Fallback for bank-like notifications:
           - Extract the first monetary amount (handles $93.150,00, 17,500, etc.).
           - Convert it properly to either a decimal string ("12.50") or integer string ("93150").
           - Extract a plausible 'place' from text around "en XYZ" or "por XYZ", removing leftover times ("21:27") or "por 17,500".
           - Default currency to "COP".

        4) Return None if we cannot parse anything meaningful or if message is filtered.
        """
        text = message_text.strip()
        if not text:
            return None

        # Apply message filtering
        message_filter = MessageFilter()
        should_filter, filter_reason = message_filter.should_filter_message(text)
        if should_filter:
            import logging
            logging.info(f"Message filtered out: {filter_reason} - '{text[:100]}...'")
            return None
        
        # -------------------------------------------------------
        # 1) Try the simple pattern first
        #    e.g. "coffee shop 12.50 usd" => place="Coffee Shop", amount="12.50", currency="USD"
        # -------------------------------------------------------
        pattern_simple = r"^(?P<place>[\w\s]+)\s+(?P<amount>[\d.,]+)(\s+(?P<currency>USD|usd|COP|cop))?$"
        simple_match = re.match(pattern_simple, text, re.IGNORECASE)
        if simple_match:
            place = simple_match.group("place").strip()
            amount_raw = simple_match.group("amount")
            currency = simple_match.group("currency") or "COP"

            # Capitalize each word in place
            place = " ".join(word.capitalize() for word in place.split())

            # Attempt to parse the amount in a "preserve decimal if truly decimal" manner
            amount_str = ExpenseInfo._normalize_amount(amount_raw)

            return ExpenseInfo(place, amount_str, currency.upper())

        # -------------------------------------------------------
        # 2) Fallback: advanced bank-like parsing
        # -------------------------------------------------------
        # A) Extract monetary amounts more carefully to avoid dates/reference numbers
        #    Look for amounts in specific contexts like "por X", "$X", or standalone amounts

        # Try to find amounts in monetary contexts first
        monetary_patterns = [
            r"por\s+\$?([\d.,]+)",  # "por 33,450" or "por $1,000.00"
            r"\$([\d.,]+)",         # "$33,450" or "$1,000.00"
            r"([\d.,]+)\s*(?:cop|usd|usdc)",  # "33,450 COP"
        ]

        raw_amount = None
        for pattern in monetary_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                raw_amount = matches[0]
                break

        # If no monetary context found, try general amount pattern but be more selective
        if not raw_amount:
            # Look for amounts but exclude obvious dates and reference numbers
            amount_pattern = r"([\d.,]+)"
            all_amounts = re.findall(amount_pattern, text)

            # Filter out amounts that look like dates, times, or reference numbers
            filtered_amounts = []
            for amount in all_amounts:
                # Skip if it looks like a date (YYYY, MM/DD, etc.)
                if re.match(r"^(19|20)\d{2}$", amount):  # Years like 2025
                    continue
                if re.match(r"^\d{1,2}[.,]\d{1,2}$", amount):  # Times like 12:20 or 06:19
                    continue
                if re.match(r"^\d{1,2}$", amount) and int(amount) <= 31:  # Days 1-31
                    continue
                if len(amount.replace(",", "").replace(".", "")) > 10:  # Very large numbers (likely IDs)
                    continue
                # Skip very small amounts that are likely not expenses
                clean_amount = amount.replace(",", "").replace(".", "")
                if clean_amount.isdigit() and int(clean_amount) < 100:  # Less than 100
                    continue

                filtered_amounts.append(amount)

            if filtered_amounts:
                raw_amount = filtered_amounts[0]

        if raw_amount:
            amount_str = ExpenseInfo._normalize_amount(raw_amount)

            # B) Derive the place
            #    i) "en Rappi Supermercado 21:27" => "Rappi Supermercado"
            #    ii) "en DLO*GOOGLE TIDAL por 17,500" => "Dlo Google Tidal"
            #    iii) If "transferencia" in text + "bancolombia" => "Bancolombia Transfer"
            place = None

            # Check if "transferencia" and "bancolombia"
            if "transferencia" in text.lower() and "bancolombia" in text.lower():
                place = "Bancolombia Transfer"

            # If not found, try capturing between "en " and " por "
            if not place:
                match_place_por = re.search(r"en\s+(.*?)\s+por\s", text, re.IGNORECASE)
                if match_place_por:
                    place = match_place_por.group(1).strip()

            # If still not found, try capturing between "en " and a time like "HH:MM"
            if not place:
                match_place_time = re.search(
                    r"en\s+(.+?)\s+\d{1,2}:\d{2}",
                    text,
                    re.IGNORECASE
                )
                if match_place_time:
                    place = match_place_time.group(1).strip()

            # If still not found, fallback
            if not place:
                place = "General"

            # Clean up place: remove punctuation like '*', ':', '.' if undesired
            place = re.sub(r"[*:\.]+", " ", place)
            # Remove " por \d+"
            place = re.sub(r"\spor\s+\d+([\.,]\d+)?", "", place, flags=re.IGNORECASE)
            # Remove leftover times if any, e.g. "21:27"
            place = re.sub(r"\d{1,2}:\d{2}", "", place)
            place = place.strip()

            # Capitalize each word
            place = " ".join(word.capitalize() for word in place.split())

            return ExpenseInfo(place, amount_str, "COP")

        # -------------------------------------------------------
        # 3) If we reached here, no parse logic worked -> Return None
        # -------------------------------------------------------
        return None

    @staticmethod
    def _normalize_amount(raw):
        """
        Takes a raw string like "12.50", "93.150,00", "17,500", "$90,000"
        and returns a cleaned numeric string that preserves decimals if appropriate
        or yields an integer string if it's effectively a whole number.
        
        Examples:
          "12.50"       -> "12.50"
          "12.0"        -> "12"    (no trailing .0)
          "93.150,00"   -> "93150"
          "17,500"      -> "17500"
          "30000"       -> "30000"
        """
        # Remove any currency symbols, spaces, etc.
        cleaned = re.sub(r"[^\d.,]", "", raw)  # keep digits, comma, dot
        cleaned = cleaned.strip()
        if not cleaned or cleaned in [".", ",", ".,"]:
            return "0"

        # If there's both '.' and ',', we need to determine which is thousands and which is decimal
        if "." in cleaned and "," in cleaned:
            # Find the last occurrence of each
            last_dot_pos = cleaned.rfind(".")
            last_comma_pos = cleaned.rfind(",")

            # The one that comes last is likely the decimal separator
            if last_comma_pos > last_dot_pos:
                # Comma is decimal separator, dots are thousands
                # E.g. "1.000,00" => "1000.00"
                part_no_dots = cleaned.replace(".", "")  # remove thousands
                unified = part_no_dots.replace(",", ".") # unify decimal
            else:
                # Dot is decimal separator, commas are thousands
                # E.g. "1,000.00" => "1000.00"
                part_no_commas = cleaned.replace(",", "")  # remove thousands
                unified = part_no_commas  # dot is already decimal

            float_val = float(unified)
            # Convert to str. If .00 => int. If not => keep decimals
            if float_val.is_integer():
                return str(int(float_val))
            else:
                return f"{float_val}".rstrip("0").rstrip(".")

        # If we only have commas or only have dots, we must guess
        # Let's handle simpler logic to pass the tests:
        # Check if it looks like "17,500" => treat ',' as thousands => remove it => "17500"
        # If "12,50" => we treat ',' as decimal => => "12.50"
        # If "12.50" => we treat '.' as decimal => => "12.50"
        # We'll do a quick check: if there's only 1 type of punctuation, see how many times it appears.
        
        # Count how many '.' and how many ','
        dot_count = cleaned.count(".")
        comma_count = cleaned.count(",")
        
        # Case A: only '.' or only ','
        if dot_count > 0 and comma_count == 0:
            # e.g. "12.50" or "17.500"
            # Heuristic: if there's exactly 1 dot and it's in the last 3 chars, treat as decimal
            # else treat as thousands
            if dot_count == 1:
                pos = cleaned.rfind(".")
                # number of digits after the dot
                digits_after = len(cleaned) - (pos + 1)
                if digits_after <= 2:
                    # decimal (including cases like "12.0")
                    try:
                        float_val = float(cleaned)
                        # if .0 => remove decimal
                        if float_val.is_integer():
                            return str(int(float_val))
                        else:
                            # Keep exactly 2 decimals if it had 2 decimals
                            # or re-stringify carefully
                            return _preserve_decimals(cleaned)
                    except ValueError:
                        # If can't convert to float, treat as thousands
                        no_dot = cleaned.replace(".", "")
                        return no_dot if no_dot else "0"
                else:
                    # treat as thousands => remove '.'
                    no_dot = cleaned.replace(".", "")
                    return no_dot
            else:
                # multiple '.' => probably "93.150.00" => weird. We'll just remove them all?
                # or interpret them as thousands?
                # For your test data, not common. We'll do a fallback: remove '.' => parse int
                no_dot = cleaned.replace(".", "")
                return no_dot

        elif comma_count > 0 and dot_count == 0:
            # e.g. "17,500" or "12,50"
            # if we see 2 digits at the end => decimal, else thousands
            if comma_count == 1:
                pos = cleaned.rfind(",")
                digits_after = len(cleaned) - (pos + 1)
                if digits_after == 2:
                    # decimal
                    unified = cleaned.replace(",", ".")
                    float_val = float(unified)
                    if float_val.is_integer():
                        return str(int(float_val))
                    else:
                        return _preserve_decimals(unified)
                else:
                    # treat as thousands => remove ','
                    return cleaned.replace(",", "")
            else:
                # multiple commas => assume all thousands except maybe last one is decimal
                # but let's keep it simple for your test data: remove them all
                return cleaned.replace(",", "")
        else:
            # No punctuation at all => just digits
            return cleaned

def _preserve_decimals(num_str):
    """
    A small helper to preserve decimal digits from a string like '12.50'.
    If it has trailing zeros after the decimal, remove them unless it’s needed.
    """
    # Convert to float, then re-stringify with minimal precision
    val = float(num_str)
    # If integer => e.g. 12.0 => "12"
    if val.is_integer():
        return str(int(val))
    else:
        # We want to preserve e.g. "12.50" as "12.50", not "12.5"
        # Easiest: get the decimal part from the original string
        # We'll assume at most 2 decimals from test data
        # If you want a more robust approach, or more than 2 decimals, adapt here
        # We'll do:
        integral, decimal = num_str.split(".")
        # remove any non-digit from decimal
        decimal = re.sub(r"[^\d]", "", decimal)
        return f"{int(integral)}.{decimal}"

