from push_notification_processor import PushNotificationProcessor
from google_sheets_client import GoogleSheetsClient

if __name__ == "__main__":
    # 1) Instantiate the GoogleSheetsClient
    sheets_client = GoogleSheetsClient(
        spreadsheet_id="1qcVKFUyDMhgUu_nohn8rqrUg0pnRF6AY6OP_ejF1yQU",
        credentials_file="google-credentials.json",
        sheet_range="All!A:I"  # The tab named ALL, columns A through I (includes categorization)
    )

    # 2) Instantiate the Processor
    processor = PushNotificationProcessor(sheets_client)

    # 3) Process a sample push notification
    processor.process_push_notification(
        app_name="BancoXYZ",
        title="Compra Aprobada",
        description="Compra por $120,000 COP en Supermercado XYZ a las 15:30. Fecha: 03/01/2025"
    )
