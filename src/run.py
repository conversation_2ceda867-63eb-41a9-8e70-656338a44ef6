#!/usr/bin/env python3
"""
Enhanced run.py with CSV migration functionality.
Provides command-line interface for processing transactions and migrating CSV data.
"""

import argparse
import csv
import sys
import os
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional

from push_notification_processor import PushNotificationProcessor
from google_sheets_client import GoogleSheetsClient
from expense_info import ExpenseInfo
from expense_categorizer import ExpenseCategorizer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CSVMigrationManager:
    """Manages the migration of CSV data to Google Sheets with improved parsing."""

    def __init__(self, spreadsheet_id: str, credentials_file: str):
        self.spreadsheet_id = spreadsheet_id
        self.credentials_file = credentials_file
        self.categorizer = ExpenseCategorizer()
        self.migration_stats = {
            'total_rows': 0,
            'successful_migrations': 0,
            'amount_corrections': 0,
            'filtered_messages': 0,
            'parsing_errors': 0,
            'categorization_stats': {},
            'significant_corrections': []
        }

    def migrate_csv_to_sheets(self, csv_path: str, sheet_name: str = None, dry_run: bool = False) -> Dict:
        """
        Migrate CSV data to Google Sheets with improved parsing and categorization.

        Args:
            csv_path (str): Path to the CSV file
            sheet_name (str): Name for the new sheet tab
            dry_run (bool): If True, only analyze without writing to sheets

        Returns:
            Dict: Migration statistics and results
        """
        logger.info(f"Starting CSV migration from {csv_path}")

        # Generate sheet name if not provided
        if not sheet_name:
            current_date = datetime.now().strftime("%Y-%m-%d")
            sheet_name = f"Migrated {current_date}"

        # Read and process CSV data
        migrated_data = self._process_csv_data(csv_path)

        if not dry_run:
            # Create new sheet and write data
            self._write_to_google_sheets(migrated_data, sheet_name)

        # Generate and display report
        self._generate_migration_report(dry_run)

        return self.migration_stats

    def _process_csv_data(self, csv_path: str) -> List[List]:
        """Process CSV data with improved parsing logic."""
        logger.info("Processing CSV data with enhanced parsing...")

        migrated_rows = []

        # Add headers
        headers = [
            "Date", "Exchange Rate", "COP", "USD", "App",
            "Description", "Categoría Principal", "Subcategoría"
        ]
        migrated_rows.append(headers)

        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)

                for row_num, row in enumerate(reader, start=2):
                    self.migration_stats['total_rows'] += 1

                    try:
                        migrated_row = self._process_single_row(row, row_num)
                        if migrated_row:
                            migrated_rows.append(migrated_row)
                            self.migration_stats['successful_migrations'] += 1
                        else:
                            self.migration_stats['filtered_messages'] += 1

                    except Exception as e:
                        logger.error(f"Error processing row {row_num}: {e}")
                        self.migration_stats['parsing_errors'] += 1

        except FileNotFoundError:
            logger.error(f"CSV file not found: {csv_path}")
            raise
        except Exception as e:
            logger.error(f"Error reading CSV file: {e}")
            raise

        return migrated_rows

    def _process_single_row(self, row: Dict, row_num: int) -> Optional[List]:
        """Process a single CSV row with enhanced parsing."""

        # Extract original data
        original_content = row.get('Content', '').strip()
        original_cop = self._safe_float(row.get('COP', '0'))
        original_usd = self._safe_float(row.get('USD', '0'))
        original_exchange_rate = self._safe_float(row.get('1 USD to COP', '4000'))
        original_date = row.get('Fecha', '')
        original_app = row.get('App', '')

        if not original_content:
            logger.warning(f"Row {row_num}: Empty content, skipping")
            return None

        # Re-parse the transaction with improved logic
        parsed_info = ExpenseInfo.parse(original_content)

        if parsed_info is None:
            # Check if this should be filtered (promotional messages, etc.)
            if self._should_be_filtered(original_content):
                logger.info(f"Row {row_num}: Correctly filtered promotional/non-expense message")
                return None
            else:
                logger.warning(f"Row {row_num}: Failed to parse valid transaction: {original_content[:50]}...")
                return None

        # Calculate corrected amounts
        parsed_amount = float(parsed_info.amount) if parsed_info.amount else 0.0

        if parsed_info.currency == "COP":
            corrected_cop = parsed_amount
            corrected_usd = round(parsed_amount / original_exchange_rate, 4) if original_exchange_rate > 0 else 0
        elif parsed_info.currency == "USD":
            corrected_usd = parsed_amount
            corrected_cop = round(parsed_amount * original_exchange_rate) if original_exchange_rate > 0 else 0
        else:
            # Default to COP
            corrected_cop = parsed_amount
            corrected_usd = round(parsed_amount / original_exchange_rate, 4) if original_exchange_rate > 0 else 0

        # Check for significant amount corrections
        cop_difference = abs(corrected_cop - original_cop) if original_cop > 0 else 0
        if cop_difference > 1000:  # Significant difference threshold
            self.migration_stats['amount_corrections'] += 1
            self.migration_stats['significant_corrections'].append({
                'row': row_num,
                'content': original_content[:80],
                'original_cop': original_cop,
                'corrected_cop': corrected_cop,
                'difference': cop_difference
            })

        # Apply hierarchical categorization
        main_category, subcategory = self.categorizer.categorize_expense(
            parsed_info.place, parsed_info.amount, original_content
        )

        # Update categorization statistics
        if main_category not in self.migration_stats['categorization_stats']:
            self.migration_stats['categorization_stats'][main_category] = 0
        self.migration_stats['categorization_stats'][main_category] += 1

        # Create migrated row
        migrated_row = [
            original_date,                    # Date
            original_exchange_rate,           # Exchange Rate
            corrected_cop,                    # COP (corrected)
            corrected_usd,                    # USD (corrected)
            original_app,                     # App
            original_content,                 # Description
            main_category,                    # Categoría Principal
            subcategory                       # Subcategoría
        ]

        return migrated_row

    def _should_be_filtered(self, content: str) -> bool:
        """Check if content should be filtered as promotional/non-expense."""
        content_lower = content.lower()

        # Promotional/marketing patterns
        promotional_patterns = [
            'descuento', 'promocion', 'oferta', 'cashback', 'dto',
            'aprovecha', 'hasta', 'solicita', 'pidela',
            'conoce mas', 'inscribete', 'aplican t&c', 'aplican tyc',
            'https://', 'http://', 'bit.ly', 'www.',
            'tasa de recarga', 'conversion', 'usd.*cop',
            'para conectarte', 'no necesitas instalar',
            'codigo', 'restablecimiento', 'login', 'new.*ip',
            'margin is low', 'liquidation', 'forced liquidated',
            'coach de bienestar', 'oportunidad'
        ]

        for pattern in promotional_patterns:
            if pattern in content_lower:
                return True

        return False

    def _safe_float(self, value: str) -> float:
        """Safely convert string to float."""
        try:
            if not value or value.strip() == '':
                return 0.0
            # Remove commas and convert
            cleaned = str(value).replace(',', '')
            return float(cleaned)
        except (ValueError, TypeError):
            return 0.0

    def _write_to_google_sheets(self, data: List[List], sheet_name: str):
        """Write migrated data to a new Google Sheets tab."""
        logger.info(f"Writing {len(data)} rows to Google Sheets tab: {sheet_name}")

        try:
            # Create sheets client for the new tab
            sheet_range = f"{sheet_name}!A:I"
            sheets_client = GoogleSheetsClient(
                spreadsheet_id=self.spreadsheet_id,
                credentials_file=self.credentials_file,
                sheet_range=sheet_range
            )

            # Create the new sheet tab
            sheets_client.create_sheet_tab(sheet_name)

            # Write all data at once
            sheets_client.batch_update_rows(data)

            logger.info(f"Successfully wrote {len(data)} rows to {sheet_name}")

        except Exception as e:
            logger.error(f"Error writing to Google Sheets: {e}")
            raise

    def _generate_migration_report(self, dry_run: bool = False):
        """Generate and display migration report."""
        stats = self.migration_stats

        print("\n" + "=" * 80)
        print("CSV MIGRATION REPORT")
        print("=" * 80)

        if dry_run:
            print("🔍 DRY RUN MODE - No data was written to Google Sheets")
            print("-" * 80)

        print(f"📊 MIGRATION STATISTICS:")
        print(f"   Total rows processed: {stats['total_rows']}")
        print(f"   Successful migrations: {stats['successful_migrations']}")
        print(f"   Filtered messages: {stats['filtered_messages']}")
        print(f"   Parsing errors: {stats['parsing_errors']}")
        print(f"   Amount corrections: {stats['amount_corrections']}")

        # Calculate success rate
        if stats['total_rows'] > 0:
            success_rate = ((stats['successful_migrations'] + stats['filtered_messages']) / stats['total_rows']) * 100
            print(f"   Success rate: {success_rate:.1f}%")

        # Show categorization statistics
        if stats['categorization_stats']:
            print(f"\n📂 CATEGORIZATION BREAKDOWN:")
            for category, count in sorted(stats['categorization_stats'].items()):
                percentage = (count / stats['successful_migrations']) * 100 if stats['successful_migrations'] > 0 else 0
                print(f"   {category}: {count} transactions ({percentage:.1f}%)")

        # Show significant corrections
        if stats['significant_corrections']:
            print(f"\n💰 SIGNIFICANT AMOUNT CORRECTIONS (showing first 10):")
            for correction in stats['significant_corrections'][:10]:
                print(f"   Row {correction['row']}: {correction['original_cop']:,.0f} → {correction['corrected_cop']:,.0f} COP")
                print(f"      Content: {correction['content']}...")
                print(f"      Difference: {correction['difference']:,.0f} COP")
                print()

        print("=" * 80)


def process_sample_transaction():
    """Process a sample transaction for testing purposes."""
    logger.info("Processing sample transaction...")

    # 1) Instantiate the GoogleSheetsClient
    sheets_client = GoogleSheetsClient(
        spreadsheet_id="1qcVKFUyDMhgUu_nohn8rqrUg0pnRF6AY6OP_ejF1yQU",
        credentials_file="google-credentials.json",
        sheet_range="All!A:I"  # The tab named ALL, columns A through I (includes categorization)
    )

    # 2) Instantiate the Processor
    processor = PushNotificationProcessor(sheets_client)

    # 3) Process a sample push notification
    processor.process_push_notification(
        app_name="BancoXYZ",
        title="Compra Aprobada",
        description="Compra por $120,000 COP en Supermercado XYZ a las 15:30. Fecha: 03/01/2025"
    )

    logger.info("Sample transaction processed successfully")


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Expense Tracker - Process transactions and migrate CSV data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process sample transaction
  python src/run.py --sample

  # Migrate CSV data to Google Sheets
  python src/run.py --migrate-csv expenses.csv

  # Dry run migration (preview only)
  python src/run.py --migrate-csv expenses.csv --dry-run

  # Custom sheet name
  python src/run.py --migrate-csv expenses.csv --sheet-name "Fixed Data 2025"

  # Migrate with custom spreadsheet
  python src/run.py --migrate-csv expenses.csv --spreadsheet-id "your-sheet-id"
        """
    )

    # Command options
    parser.add_argument(
        '--sample',
        action='store_true',
        help='Process a sample transaction for testing'
    )

    parser.add_argument(
        '--migrate-csv',
        type=str,
        metavar='CSV_FILE',
        help='Migrate data from CSV file to Google Sheets with improved parsing'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Preview migration changes without writing to Google Sheets'
    )

    parser.add_argument(
        '--sheet-name',
        type=str,
        metavar='NAME',
        help='Custom name for the new sheet tab (default: "Migrated YYYY-MM-DD")'
    )

    parser.add_argument(
        '--spreadsheet-id',
        type=str,
        default="1qcVKFUyDMhgUu_nohn8rqrUg0pnRF6AY6OP_ejF1yQU",
        help='Google Sheets spreadsheet ID (default: configured spreadsheet)'
    )

    parser.add_argument(
        '--credentials',
        type=str,
        default="google-credentials.json",
        help='Path to Google credentials JSON file (default: google-credentials.json)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        if args.migrate_csv:
            # CSV Migration mode
            logger.info(f"Starting CSV migration from {args.migrate_csv}")

            if not os.path.exists(args.migrate_csv):
                logger.error(f"CSV file not found: {args.migrate_csv}")
                sys.exit(1)

            if args.dry_run:
                logger.info("Running in DRY RUN mode - no data will be written to Google Sheets")

            # Create migration manager
            migration_manager = CSVMigrationManager(
                spreadsheet_id=args.spreadsheet_id,
                credentials_file=args.credentials
            )

            # Perform migration
            results = migration_manager.migrate_csv_to_sheets(
                csv_path=args.migrate_csv,
                sheet_name=args.sheet_name,
                dry_run=args.dry_run
            )

            # Exit with appropriate code
            if results['parsing_errors'] > 0:
                logger.warning(f"Migration completed with {results['parsing_errors']} errors")
                sys.exit(1)
            else:
                logger.info("Migration completed successfully")
                sys.exit(0)

        elif args.sample:
            # Sample transaction mode
            process_sample_transaction()

        else:
            # No command specified, show help
            parser.print_help()
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
