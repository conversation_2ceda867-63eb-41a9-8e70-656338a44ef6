import re
from typing import Tuple, Dict, List

class ExpenseCategorizer:
    """
    Automatic expense categorization system that assigns main categories and subcategories
    based on merchant names, transaction patterns, and keywords.
    """
    
    def __init__(self):
        """Initialize the categorizer with predefined patterns and mappings."""
        
        # Main categories
        self.MAIN_CATEGORIES = {
            'INGRESOS': 'Ingresos',
            'GASTOS_ESENCIALES': 'Gastos Esenciales',
            'GASTOS_DISCRECIONALES': 'Gastos Discrecionales',
            'PAGO_DEUDAS': 'Pago de Deudas',
            'AHORROS': 'Ahorros',
            'INVERSIONES': 'Inversiones'
        }
        
        # Subcategories mapping based on the budget image
        self.SUBCATEGORIES = {
            # INGRESOS
            'INGRESOS': {
                'Salario': ['salario', 'sueldo', 'nomina', 'pago empresa', 'salary'],
                'Freelance - Clau': ['freelance', 'trabajo independiente', 'consultoría'],
                'Freelance - Euros': ['freelance euro', 'trabajo europa', 'consultoría internacional'],
                'Ventas': ['venta', 'selling', 'marketplace'],
                'Fondos Tri': ['fondo', 'investment return', 'dividendo'],
                'Ventas Crypto': ['binance.*venta', 'venta.*crypto', 'venta.*bitcoin', 'venta.*ethereum'],
                'Pago Prestamos': ['prestamo recibido', 'loan received']
            },
            
            # GASTOS ESENCIALES
            'GASTOS_ESENCIALES': {
                'Mercado': ['supermercado', 'mercado', 'grocery', 'rappi', 'domicilios', 'exito', 'carulla', 'olimpica'],
                'Gym': ['gym', 'gimnasio', 'bodytech', 'fitness', 'entrenamiento'],
                'Samson': ['samson', 'medicamentos', 'farmacia', 'drogueria'],
                'Salud': ['salud', 'medico', 'doctor', 'hospital', 'clinica', 'eps'],
                'Prepagada': ['prepagada', 'medicina prepagada', 'seguro medico'],
                'Admin Qz': ['administracion', 'admin', 'cuota administracion'],
                'Servicios': ['epm', 'servicios publicos', 'luz', 'agua', 'gas', 'telefono'],
                'Casa Internet': ['claro.*internet', 'internet casa', 'wifi', 'broadband'],
                'Admin Qz Extra': ['admin extra', 'cuota extra'],
                'Casa Servicios': ['servicios casa', 'utilities']
            },
            
            # GASTOS DISCRECIONALES
            'GASTOS_DISCRECIONALES': {
                'Comida Rapida': ['mcdonalds', 'mcdonald', 'burger king', 'kfc', 'subway', 'pizza', 'dominos', 'comida.*rapida'],
                'Subs DFY': [r'^netflix$', r'^spotify$', 'amazon prime', 'subscription', 'suscripcion', 'google.*tidal', 'dlo.*google'],
                'Subs FUN': ['steam', 'gaming', 'entertainment', 'entretenimiento'],
                'Banco Mango': ['banco.*comision', 'comision bancaria', 'fee.*banco', 'bancolombia.*transfer'],
                'Scotis Seguros': ['scotia.*seguro', 'seguro.*scotia', 'insurance.*scotia'],
                'Estudio o Libros': ['libro', 'book', 'curso', 'education', 'udemy'],
                'Paseo': ['paseo', 'turismo', 'viaje', 'travel'],
                'Regalo': ['regalo', 'gift', 'present'],
                'Ropa': ['ropa', 'clothes', 'fashion', 'zara', 'h&m'],
                'Uber': [r'^uber$', 'taxi.*viaje', 'cabify'],
                'Carro Gasolina': [r'^terpel$', r'^esso$', 'gasolina', 'combustible', 'gas station'],
                'Carro Parqueadero': ['parqueadero', 'parking', 'estacionamiento'],
                'Carro Gastos': ['carro', 'vehiculo', 'car maintenance', 'mecanico'],
                'Casa Mercado': ['home depot', 'homecenter', 'mercado casa'],
                'Casa Varios': ['casa varios', 'hogar', 'home'],
                'Casa Limpieza': ['limpieza', 'cleaning', 'detergente', 'jabon'],
                'Hobbies': ['hobby', 'pasatiempo', 'juego', 'game'],
                'Nomada': ['nomada', 'coworking', 'workspace']
            },
            
            # PAGO DE DEUDAS
            'PAGO_DEUDAS': {
                'Prestamo Clau': [r'^scotia$.*cuota', r'^scotia$.*prestamo', 'prestamo', 'loan payment', 'credito'],
                'Tarjeta Cr': ['pago.*tarjeta', 'tarjeta credito', 'credit card', 'visa.*pago', 'mastercard.*pago']
            },

            # AHORROS
            'AHORROS': {
                'Pago Futuros': ['banco.*ahorro', 'ahorro', 'savings'],
                'Colchon': ['emergency fund', 'colchon', 'emergency', 'fondo emergencia'],
                'USD': ['compra.*usd', 'casa.*cambio', 'usd', 'dollar', 'dolar']
            },
            
            # INVERSIONES
            'INVERSIONES': {
                'Cripto': ['crypto', 'bitcoin', 'ethereum', 'binance'],
                'Acciones': ['acciones', 'stocks', 'bolsa'],
                'Otros': ['inversion', 'investment', 'portfolio'],
                'ETF': ['etf', 'index fund', 'fondo indexado']
            }
        }
        
        # Income patterns (for detecting income vs expenses)
        self.income_patterns = [
            r'salario', r'sueldo', r'nomina', r'pago empresa',
            r'freelance', r'consultoría', r'trabajo independiente',
            r'venta', r'selling', r'dividendo', r'prestamo recibido'
        ]
        
        # Debt payment patterns
        self.debt_patterns = [
            r'pago.*tarjeta', r'credit card', r'prestamo', r'loan payment',
            r'cuota.*credito', r'visa.*pago', r'mastercard.*pago', r'pago.*credito'
        ]
        
        # Savings patterns
        self.savings_patterns = [
            r'ahorro', r'savings', r'emergency fund', r'fondo.*emergencia',
            r'colchon', r'reserva'
        ]
        
        # Investment patterns (more specific)
        self.investment_patterns = [
            r'compra.*crypto', r'compra.*bitcoin', r'compra.*ethereum', r'binance.*compra',
            r'compra.*acciones', r'stocks.*compra', r'bolsa.*inversion', r'etf.*compra', r'investment.*portfolio'
        ]

    def categorize_expense(self, place: str, amount: str, description: str = "") -> Tuple[str, str]:
        """
        Categorize an expense based on place, amount, and description.

        Args:
            place (str): The merchant/place name
            description (str): Full transaction description
            amount (str): Transaction amount

        Returns:
            Tuple[str, str]: (main_category, subcategory)
        """
        combined_text = f"{place} {description}".lower().strip()

        # Check specific place patterns first (highest priority)
        specific_result = self._check_specific_place_patterns(place, description)
        if specific_result[0] is not None:
            return specific_result

        # Check for income patterns
        if self._matches_patterns(combined_text, self.income_patterns):
            return self._find_subcategory('INGRESOS', combined_text)

        # Check for debt payments
        if self._matches_patterns(combined_text, self.debt_patterns):
            return self._find_subcategory('PAGO_DEUDAS', combined_text)

        # Check for savings
        if self._matches_patterns(combined_text, self.savings_patterns):
            return self._find_subcategory('AHORROS', combined_text)

        # Check for investments
        if self._matches_patterns(combined_text, self.investment_patterns):
            return self._find_subcategory('INVERSIONES', combined_text)

        # Check essential expenses
        main_cat, sub_cat = self._find_subcategory('GASTOS_ESENCIALES', combined_text)
        if sub_cat != 'Otros':
            return main_cat, sub_cat

        # Default to discretionary expenses
        return self._find_subcategory('GASTOS_DISCRECIONALES', combined_text)

    def _matches_patterns(self, text: str, patterns: List[str]) -> bool:
        """Check if text matches any of the given patterns."""
        for pattern in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False

    def _find_subcategory(self, main_category_key: str, text: str) -> Tuple[str, str]:
        """
        Find the best matching subcategory for a given main category.

        Args:
            main_category_key (str): Key for the main category
            text (str): Text to match against

        Returns:
            Tuple[str, str]: (main_category, subcategory)
        """
        main_category = self.MAIN_CATEGORIES[main_category_key]
        subcategories = self.SUBCATEGORIES[main_category_key]

        # Try to find matching subcategory
        for subcategory, keywords in subcategories.items():
            for keyword in keywords:
                if re.search(keyword, text, re.IGNORECASE):
                    return main_category, subcategory

        # Default subcategory if no match found
        return main_category, 'Otros'

    def _check_specific_place_patterns(self, place: str, description: str) -> Tuple[str, str]:
        """Check for specific place name patterns that should override general patterns."""
        place_lower = place.lower()
        desc_lower = description.lower()

        # Specific place mappings
        specific_mappings = {
            'netflix': ('Gastos Discrecionales', 'Subs DFY'),
            'spotify': ('Gastos Discrecionales', 'Subs DFY'),
            'uber': ('Gastos Discrecionales', 'Uber'),
            'terpel': ('Gastos Discrecionales', 'Carro Gasolina'),
            'esso': ('Gastos Discrecionales', 'Carro Gasolina'),
            'bodytech': ('Gastos Esenciales', 'Gym'),
            'rappi': ('Gastos Esenciales', 'Mercado'),
            'exito': ('Gastos Esenciales', 'Mercado'),
            'carulla': ('Gastos Esenciales', 'Mercado'),
            'bancolombia transfer': ('Gastos Discrecionales', 'Otros'),
        }

        for place_pattern, (main_cat, sub_cat) in specific_mappings.items():
            if place_pattern in place_lower:
                return main_cat, sub_cat

        # Check for debt payment patterns in description
        if 'pago' in desc_lower and ('tarjeta' in desc_lower or 'credito' in desc_lower):
            return 'Pago de Deudas', 'Tarjeta Cr'

        if 'scotia' in place_lower and ('cuota' in desc_lower or 'prestamo' in desc_lower):
            return 'Pago de Deudas', 'Prestamo Clau'

        # Check for savings patterns
        if 'emergency fund' in desc_lower:
            return 'Ahorros', 'Colchon'

        if 'compra' in desc_lower and 'usd' in desc_lower:
            return 'Ahorros', 'USD'

        # Check for income patterns
        if 'binance' in place_lower and 'venta' in desc_lower and 'crypto' in desc_lower:
            return 'Ingresos', 'Ventas Crypto'

        # Check for investment patterns
        if ('etf' in place_lower and 'inversion' in desc_lower) or 'etf platform' in place_lower:
            return 'Inversiones', 'ETF'

        return None, None

    def get_all_categories(self) -> Dict[str, List[str]]:
        """Get all available categories and subcategories."""
        result = {}
        for main_key, main_name in self.MAIN_CATEGORIES.items():
            result[main_name] = list(self.SUBCATEGORIES[main_key].keys())
        return result
