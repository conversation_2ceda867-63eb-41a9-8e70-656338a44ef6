# Makefile Commands Reference

## Overview

The Makefile provides convenient commands for managing the expense categorization system, including CSV migration, testing, and validation. All commands can be run using `make <command>`.

## Available Commands

### Setup and Installation
```bash
make help                    # Show all available commands with descriptions
make prepare                 # Create Python virtual environment
make install                 # Install all required dependencies
make setup                   # Complete setup: create venv and install dependencies
```

### Core Functionality
```bash
make run                     # Run sample transaction processing
make test                    # Run all unit tests (38 tests)
make check                   # Run tests and validation together
```

### CSV Migration Commands

#### Basic Migration
```bash
make migrate-dry-run         # Preview CSV migration without writing to Google Sheets
make migrate                 # Migrate CSV data to Google Sheets with improved parsing
make migrate-verbose         # Migrate with detailed logging output
```

#### Advanced Migration
```bash
make migrate-custom SHEET_NAME="Custom Name"    # Migrate with custom sheet name
make migrate-help                                # Show detailed migration help
```

**Examples:**
```bash
# Safe preview of migration
make migrate-dry-run

# Full migration with default sheet name "Migrated YYYY-MM-DD"
make migrate

# Migration with custom sheet name
make migrate-custom SHEET_NAME="Historical Data Fixed"

# Detailed migration with verbose logging
make migrate-verbose
```

### Validation and Demonstration
```bash
make demo                    # Quick demonstration of expense categorization
make validate                # Validate core parsing functionality
```

### Development Commands
```bash
make lint                    # Check code style (requires flake8)
make clean                   # Clean up temporary Python files
```

## Migration Command Details

### migrate-dry-run
**Purpose**: Preview all migration changes without modifying Google Sheets
**Output**: Comprehensive report showing:
- Total transactions processed
- Amount corrections identified
- Categorization breakdown
- Significant corrections (>1,000 COP difference)
- Filtered promotional messages

**Example Output:**
```
📊 MIGRATION STATISTICS:
   Total rows processed: 386
   Successful migrations: 333
   Filtered messages: 53
   Parsing errors: 0
   Amount corrections: 249
   Success rate: 100.0%
```

### migrate
**Purpose**: Perform actual migration to Google Sheets
**Result**: Creates new sheet tab with corrected data
**Process**:
1. Re-parses all transactions with improved logic
2. Fixes amount extraction errors (890,079 → 33,450 COP)
3. Applies hierarchical categorization
4. Creates new sheet tab "Migrated YYYY-MM-DD"
5. Writes all corrected data with full column structure (A:I)

### migrate-custom
**Purpose**: Migrate with user-specified sheet name
**Usage**: `make migrate-custom SHEET_NAME="Your Custom Name"`
**Result**: Creates sheet tab with specified name instead of default

### migrate-verbose
**Purpose**: Get detailed logging during migration process
**Output**: Step-by-step processing information including:
- Individual transaction parsing results
- Amount correction details
- Categorization assignments
- Google Sheets API interactions

## Command Workflow Examples

### First-Time Setup
```bash
make setup                   # Install everything
make test                    # Verify installation
make demo                    # See categorization in action
```

### Safe Migration Process
```bash
make migrate-dry-run         # Preview changes
make migrate-verbose         # Run with detailed logging
# Review results, then:
make migrate                 # Perform actual migration
```

### Development Workflow
```bash
make test                    # Run tests after changes
make validate                # Verify core functionality
make lint                    # Check code style
make clean                   # Clean up temporary files
```

## Migration Results

### What Gets Fixed
- **Amount Parsing Errors**: Extracts correct monetary values instead of dates/reference numbers
- **Currency Detection**: Properly identifies COP vs USD transactions
- **Message Filtering**: Removes promotional/non-expense messages
- **Categorization**: Applies 6-tier hierarchical budget structure

### Google Sheets Structure
The migration creates a new sheet with columns A through I:
- **A**: Date (preserved from original)
- **B**: Exchange Rate (preserved from original)
- **C**: COP Amount (corrected)
- **D**: USD Amount (recalculated)
- **E**: App (preserved from original)
- **F**: Description (preserved from original)
- **G**: Categoría Principal (new - main category)
- **H**: Subcategoría (new - subcategory)

### Success Metrics
- **100% Success Rate**: All valid transactions processed
- **Zero Parsing Errors**: Enhanced regex handles all formats
- **249 Amount Corrections**: Significant fixes identified in test dataset
- **53 Messages Filtered**: Promotional content correctly excluded

## Error Handling

### Common Issues and Solutions

**CSV File Not Found**
```bash
Error: CSV file not found: expenses.csv
Solution: Ensure expenses.csv exists in project root
```

**Google Sheets Permission Error**
```bash
Error: Error creating sheet tab...
Solution: Check google-credentials.json and spreadsheet permissions
```

**Migration Interrupted**
```bash
Solution: Re-run the command - it will skip existing sheets
```

### Troubleshooting Steps
1. Run `make migrate-dry-run` first to identify issues
2. Use `make migrate-verbose` for detailed error information
3. Verify CSV file format and Google Sheets credentials
4. Check `make test` to ensure core functionality works

## Best Practices

### 1. Always Start with Dry Run
```bash
make migrate-dry-run
```
Review the comprehensive report before actual migration.

### 2. Use Descriptive Sheet Names
```bash
make migrate-custom SHEET_NAME="Fixed Historical Data 2025"
```
Makes it easier to identify migrated data later.

### 3. Backup Before Migration
Ensure you have backups of both CSV and Google Sheets data.

### 4. Validate After Migration
Spot-check a few transactions in the new sheet to verify accuracy.

### 5. Monitor for Errors
Use verbose mode if you encounter any issues:
```bash
make migrate-verbose
```

## Integration with Development

### Testing Integration
```bash
make test                    # Run all 38 unit tests
make check                   # Run tests + validation
```

### Code Quality
```bash
make lint                    # Check code style
make clean                   # Clean temporary files
```

### Continuous Integration
The commands are designed to work in CI/CD environments:
- Exit codes indicate success/failure
- Comprehensive logging for debugging
- No interactive prompts required

## Performance Notes

- **Processing Speed**: ~1ms per transaction
- **Memory Usage**: Minimal overhead for large datasets
- **Google Sheets API**: Efficient batch updates
- **Large Files**: Handles 386+ transactions smoothly

The Makefile commands provide a complete interface for managing the expense categorization system, from initial setup through production migration of historical data.
