import unittest
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from expense_categorizer import ExpenseCategorizer


class TestExpenseCategorizer(unittest.TestCase):
    
    def setUp(self):
        self.categorizer = ExpenseCategorizer()
    
    def test_income_categorization(self):
        """Test categorization of income transactions"""
        test_cases = [
            ("Empresa ABC", "Pago salario enero", "Ingresos", "Salario"),
            ("Freelance Client", "Pago consultoría", "Ingresos", "Freelance - Clau"),
            ("Marketplace", "Venta producto", "Ingresos", "Ventas"),
            ("Binance", "Venta crypto", "Ingresos", "Ventas Crypto"),
        ]
        
        for place, description, expected_main, expected_sub in test_cases:
            with self.subTest(place=place, description=description):
                main_cat, sub_cat = self.categorizer.categorize_expense(place, "1000", description)
                self.assertEqual(main_cat, expected_main, f"Main category mismatch for {place}")
                self.assertEqual(sub_cat, expected_sub, f"Subcategory mismatch for {place}")
    
    def test_essential_expenses_categorization(self):
        """Test categorization of essential expenses"""
        test_cases = [
            ("Rappi Supermercado", "Compra mercado", "Gastos Esenciales", "Mercado"),
            ("Exito", "Compra supermercado", "Gastos Esenciales", "Mercado"),
            ("BodyTech", "Mensualidad gym", "Gastos Esenciales", "Gym"),
            ("Drogueria Samson", "Medicamentos", "Gastos Esenciales", "Samson"),
            ("Hospital San Vicente", "Consulta médica", "Gastos Esenciales", "Salud"),
            ("EPM", "Servicios públicos", "Gastos Esenciales", "Servicios"),
            ("Claro Internet", "Internet casa", "Gastos Esenciales", "Casa Internet"),
        ]
        
        for place, description, expected_main, expected_sub in test_cases:
            with self.subTest(place=place, description=description):
                main_cat, sub_cat = self.categorizer.categorize_expense(place, "50000", description)
                self.assertEqual(main_cat, expected_main, f"Main category mismatch for {place}")
                self.assertEqual(sub_cat, expected_sub, f"Subcategory mismatch for {place}")
    
    def test_discretionary_expenses_categorization(self):
        """Test categorization of discretionary expenses"""
        test_cases = [
            ("McDonald's", "Comida rápida", "Gastos Discrecionales", "Comida Rapida"),
            ("Netflix", "Suscripción mensual", "Gastos Discrecionales", "Subs DFY"),
            ("Steam", "Juego nuevo", "Gastos Discrecionales", "Subs FUN"),
            ("Uber", "Viaje centro", "Gastos Discrecionales", "Uber"),
            ("Terpel", "Gasolina carro", "Gastos Discrecionales", "Carro Gasolina"),
            ("Zara", "Ropa nueva", "Gastos Discrecionales", "Ropa"),
            ("Librería Nacional", "Libro programación", "Gastos Discrecionales", "Estudio o Libros"),
        ]
        
        for place, description, expected_main, expected_sub in test_cases:
            with self.subTest(place=place, description=description):
                main_cat, sub_cat = self.categorizer.categorize_expense(place, "25000", description)
                self.assertEqual(main_cat, expected_main, f"Main category mismatch for {place}")
                self.assertEqual(sub_cat, expected_sub, f"Subcategory mismatch for {place}")
    
    def test_debt_payments_categorization(self):
        """Test categorization of debt payments"""
        test_cases = [
            ("Bancolombia", "Pago tarjeta crédito", "Pago de Deudas", "Tarjeta Cr"),
            ("Scotia", "Cuota préstamo", "Pago de Deudas", "Prestamo Clau"),
            ("Visa", "Pago credit card", "Pago de Deudas", "Tarjeta Cr"),
        ]
        
        for place, description, expected_main, expected_sub in test_cases:
            with self.subTest(place=place, description=description):
                main_cat, sub_cat = self.categorizer.categorize_expense(place, "100000", description)
                self.assertEqual(main_cat, expected_main, f"Main category mismatch for {place}")
                self.assertEqual(sub_cat, expected_sub, f"Subcategory mismatch for {place}")
    
    def test_savings_categorization(self):
        """Test categorization of savings"""
        test_cases = [
            ("Banco", "Ahorro mensual", "Ahorros", "Pago Futuros"),
            ("Cuenta Ahorros", "Emergency fund", "Ahorros", "Colchon"),
            ("Casa Cambio", "Compra USD", "Ahorros", "USD"),
        ]
        
        for place, description, expected_main, expected_sub in test_cases:
            with self.subTest(place=place, description=description):
                main_cat, sub_cat = self.categorizer.categorize_expense(place, "200000", description)
                self.assertEqual(main_cat, expected_main, f"Main category mismatch for {place}")
                self.assertEqual(sub_cat, expected_sub, f"Subcategory mismatch for {place}")
    
    def test_investments_categorization(self):
        """Test categorization of investments"""
        test_cases = [
            ("Binance", "Compra Bitcoin", "Inversiones", "Cripto"),
            ("Broker", "Compra acciones", "Inversiones", "Acciones"),
            ("ETF Platform", "Inversión ETF", "Inversiones", "ETF"),
        ]
        
        for place, description, expected_main, expected_sub in test_cases:
            with self.subTest(place=place, description=description):
                main_cat, sub_cat = self.categorizer.categorize_expense(place, "500000", description)
                self.assertEqual(main_cat, expected_main, f"Main category mismatch for {place}")
                self.assertEqual(sub_cat, expected_sub, f"Subcategory mismatch for {place}")
    
    def test_default_categorization(self):
        """Test default categorization for unrecognized expenses"""
        main_cat, sub_cat = self.categorizer.categorize_expense("Unknown Store", "50000", "Random purchase")
        self.assertEqual(main_cat, "Gastos Discrecionales")
        self.assertEqual(sub_cat, "Otros")
    
    def test_case_insensitive_matching(self):
        """Test that categorization is case insensitive"""
        test_cases = [
            ("RAPPI SUPERMERCADO", "COMPRA MERCADO"),
            ("rappi supermercado", "compra mercado"),
            ("Rappi Supermercado", "Compra Mercado"),
        ]
        
        expected_main = "Gastos Esenciales"
        expected_sub = "Mercado"
        
        for place, description in test_cases:
            with self.subTest(place=place, description=description):
                main_cat, sub_cat = self.categorizer.categorize_expense(place, "50000", description)
                self.assertEqual(main_cat, expected_main)
                self.assertEqual(sub_cat, expected_sub)
    
    def test_get_all_categories(self):
        """Test getting all available categories"""
        categories = self.categorizer.get_all_categories()
        
        # Check that all main categories are present
        expected_main_categories = [
            "Ingresos", "Gastos Esenciales", "Gastos Discrecionales",
            "Pago de Deudas", "Ahorros", "Inversiones"
        ]
        
        for main_cat in expected_main_categories:
            self.assertIn(main_cat, categories)
            self.assertIsInstance(categories[main_cat], list)
            self.assertGreater(len(categories[main_cat]), 0)
    
    def test_real_world_examples(self):
        """Test with real-world transaction examples"""
        test_cases = [
            # Bank transaction examples
            ("Rappi Supermercado", "Compra por $93.150,00 en RAPPI SUPERMERCADO", "Gastos Esenciales", "Mercado"),
            ("Dlo Google Tidal", "Scotiabank Colpatria: Realizaste transaccion en DLO*GOOGLE TIDAL", "Gastos Discrecionales", "Subs DFY"),
            ("Bancolombia Transfer", "Transferencia por $90,000", "Gastos Discrecionales", "Otros"),
            
            # Simple format examples
            ("Coffee Shop", "coffee shop 12.50 usd", "Gastos Discrecionales", "Otros"),
            ("Restaurant", "Restaurant 30000 COP", "Gastos Discrecionales", "Otros"),
        ]
        
        for place, description, expected_main, expected_sub in test_cases:
            with self.subTest(place=place, description=description):
                main_cat, sub_cat = self.categorizer.categorize_expense(place, "50000", description)
                self.assertEqual(main_cat, expected_main, f"Main category mismatch for {place}")
                self.assertEqual(sub_cat, expected_sub, f"Subcategory mismatch for {place}")


if __name__ == '__main__':
    unittest.main()
