# tests/test_expense_info.py

import unittest
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

from expense_info import ExpenseInfo

class TestExpenseInfo(unittest.TestCase):
    def test_valid_usd_message(self):
        message_text = "Coffee Shop 12.50 USD"
        expected = ExpenseInfo("Coffee Shop", "12.50", "USD")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_valid_cop_message(self):
        message_text = "cop44,900.00 with littio"
        expected = ExpenseInfo("General", "44900", "COP")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_valid_google_wallet_cop_message(self):
        message_text = "Restaurant 30000 COP"
        expected = ExpenseInfo("Restaurant", "30000", "COP")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_capitalize_small_message(self):
        message_text = "coffee shop 12.50 usd"
        expected = ExpenseInfo("Coffee Shop", "12.50", "USD")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_capitalize_full_message(self):
        message_text = "coffee shop 12.50"
        expected = ExpenseInfo("Coffee Shop", "12.50", "COP")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_valid_message_without_currency(self):
        message_text = "Bakery 4500"
        expected = ExpenseInfo("Bakery", "4500", "COP")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_valid_bank_transference_message_without_currency(self):
        message_text = (
            "Bancolombia le informa Transferencia por $90,000 "
            "desde cta *9534 a cta ***********. 10/07/2024 17:54. "
            "Inquietudes al **********/************."
        )
        expected = ExpenseInfo("Bancolombia Transfer", "90000", "COP")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_valid_bank_purchase_message_without_currency(self):
        message_text = (
            "Bancolombia le informa Compra por $93.150,00 "
            "en RAPPI SUPERMERCADO 21:27. 13/07/2024 T.Cred *1074. "
            "Inquietudes al **********/************."
        )
        expected = ExpenseInfo("Rappi Supermercado", "93150", "COP")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_valid_scotiabank_colpatria_message(self):
        """
        Scotia example:
        "Scotiabank Colpatria: Realizaste transaccion en DLO*GOOGLE TIDAL por 17,500
         con tu tarjeta Visa Platinum 2025/01/02 7:15:26"
        We expect the place to be "Dlo Google Tidal" (capitalized), 
        the amount to be "17500", and default currency "COP" since it isn't specified.
        """
        message_text = (
            "Scotiabank Colpatria: Realizaste transaccion en DLO*GOOGLE TIDAL por 17,500 "
            "con tu tarjeta Visa Platinum 2025/01/02 7:15:26"
        )
        expected = ExpenseInfo("Dlo Google Tidal", "17500", "COP")
        result = ExpenseInfo.parse(message_text)
        self.assertEqual(str(result), str(expected))

    def test_invalid_message(self):
        message_text = "Invalid message format"
        result = ExpenseInfo.parse(message_text)
        self.assertIsNone(result)

    def test_usd_littio_conversion_message_returns_none(self):
        message_text = (
            "la tasa de recarga es de $4.140 cop, y la conversión de usd a cop está en $4.040 cop. bajó $25 cop desde ayer"
        )
        result = ExpenseInfo.parse(message_text)
        self.assertIsNone(result)
    
    def test_normalize_amount(self):
        test_cases = [
            ("12.50", "12.50"),
            ("12.0", "12"),
            ("93.150,00", "93150"),
            ("17,500", "17500"),
            ("30000", "30000"),
            ("1,000.00", "1000"),
            ("1.000,00", "1000"),
            ("1,000", "1000"),
            ("1.000", "1000"),
            ("1,0000", "10000"),
            ("$90,000", "90000"),
            ("€1.234,56", "1234.56"),
        ]
        for raw, expected in test_cases:
            with self.subTest(raw=raw):
                self.assertEqual(ExpenseInfo._normalize_amount(raw), expected)


if __name__ == '__main__':
    unittest.main()
