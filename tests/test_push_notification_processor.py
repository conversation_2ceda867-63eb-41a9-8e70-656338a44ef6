import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime
from push_notification_processor import PushNotificationProcessor, GoogleSheetsClient

class TestPushNotificationProcessor(unittest.TestCase):
    def setUp(self):
        self.mock_sheets_client = MagicMock(spec=GoogleSheetsClient)

    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_process_push_notification_with_cop(self, mock_expense_info, mock_exchange_rate):
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "COP"
        mock_parsed_info.amount = "120000"
        mock_expense_info.parse.return_value = mock_parsed_info

        processor = PushNotificationProcessor(self.mock_sheets_client)
        processor.process_push_notification(app_name="any", title="any", description="any")

        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(appended_row[2], 120000.0)  # COP value as float
        self.assertEqual(appended_row[3], 30.0)      # USD value as float (120000 / 4000)
        self.assertEqual(appended_row[4], "any")     # App name

    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_process_push_notification_with_usd(self, mock_expense_info, mock_exchange_rate):
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "USD"
        mock_parsed_info.amount = "100"
        mock_expense_info.parse.return_value = mock_parsed_info

        processor = PushNotificationProcessor(self.mock_sheets_client)
        processor.process_push_notification(app_name="BancoABC", title="any", description="any")

        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(appended_row[2], 400000.0)  # COP value as float (100 * 4000)
        self.assertEqual(appended_row[3], 100.0)     # USD value as float
        self.assertEqual(appended_row[4], "BancoABC")# App name

    @patch('push_notification_processor.ExpenseInfo')
    def test_process_push_notification_invalid_text(self, mock_expense_info):
        mock_expense_info.parse.return_value = None

        processor = PushNotificationProcessor(self.mock_sheets_client)
        processor.process_push_notification(app_name="any", title="any", description="any")

        self.mock_sheets_client.append_row.assert_not_called()

    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_process_push_notification_unknown_currency(self, mock_expense_info, mock_exchange_rate):
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "EUR"  # Unsupported currency
        mock_parsed_info.amount = "50"
        mock_expense_info.parse.return_value = mock_parsed_info

        processor = PushNotificationProcessor(self.mock_sheets_client)
        processor.process_push_notification(app_name="any", title="any", description="any")

        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(appended_row[2], 50.0)  # COP value as float
        self.assertAlmostEqual(appended_row[3], 0.0125, places=4)  # USD equivalent (50 / 4000)
        self.assertEqual(appended_row[4], "any")  # App name

if __name__ == '__main__':
    unittest.main()
