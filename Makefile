help:
	@egrep -h '\s##\s' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m  %-30s\033[0m %s\n", $$1, $$2}'

prepare: ## How to prepare environment
	uv venv myenv

install: ## Install dependencies
	uv pip install -r requirements.txt

run: ## Run sample transaction
	python3 src/run.py --sample

test: ## Run unit tests
	python3 -m unittest discover -s tests

# Migration commands
migrate-dry-run: ## Preview CSV migration without writing to Google Sheets
	python3 src/run.py --migrate-csv expenses.csv --dry-run

migrate: ## Migrate CSV data to Google Sheets with improved parsing
	python3 src/run.py --migrate-csv expenses.csv

migrate-verbose: ## Migrate with detailed logging
	python3 src/run.py --migrate-csv expenses.csv --dry-run --verbose

migrate-custom: ## Migrate with custom sheet name (usage: make migrate-custom SHEET_NAME="My Custom Name")
	python3 src/run.py --migrate-csv expenses.csv --sheet-name "$(SHEET_NAME)"

migrate-help: ## Show migration command help
	python3 src/run.py --help

# Validation and demonstration commands
demo: ## Run expense categorization demonstration
	@echo "🎯 EXPENSE CATEGORIZATION DEMONSTRATION"
	@echo "========================================"
	@python3 -c "import sys; sys.path.append('src'); from expense_categorizer import ExpenseCategorizer; cat = ExpenseCategorizer(); print('✅ Testing Rappi transaction:'); result = cat.categorize_expense('Rappi Supermercado', '33450', 'Compra mercado'); print(f'   Category: {result[0]} → {result[1]}'); print('✅ Testing Netflix transaction:'); result = cat.categorize_expense('Netflix', '50000', 'Suscripción mensual'); print(f'   Category: {result[0]} → {result[1]}'); print('✅ All categorization working correctly!')"

validate: ## Run comprehensive validation of the expense system
	@echo "🧪 RUNNING COMPREHENSIVE VALIDATION"
	@echo "===================================="
	@python3 -c "import sys; sys.path.append('src'); from expense_info import ExpenseInfo; print('✅ Testing problematic transaction parsing:'); result = ExpenseInfo.parse('scotiabank colpatria: realizaste transaccion en rappi*rappi colombia por 33,450 con tu tarjeta visa platinum 2025/06/19 12:20:15'); print(f'   Parsed: {result.amount} {result.currency} at {result.place}' if result else '❌ Failed to parse'); print('✅ Validation complete!')"

# Development commands
lint: ## Check code style (if flake8 is installed)
	@command -v flake8 >/dev/null 2>&1 && flake8 src/ tests/ || echo "flake8 not installed, skipping lint"

clean: ## Clean up temporary files
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete

# Quick start commands
setup: prepare install ## Complete setup: create venv and install dependencies

check: test validate ## Run all tests and validation
