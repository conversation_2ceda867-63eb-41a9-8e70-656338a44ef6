import appdaemon.plugins.hass.hassapi as hass
import requests
from datetime import datetime, timedelta

from google_sheets_client import GoogleSheetsClient
from push_notification_processor import PushNotificationProcessor

import os

class NotificationHandlerTask(hass.Hass):
    def initialize(self):
        self.log(f"[NotificationHandlerTask] starting waiting")
        self.listen_event(self.handle, "NOTIFICATION_HANDLER_TASK")

    def fetch_value(self, data, key):
        value = data.get(key, "")

        if isinstance(value, int):
            return str(value)
        if isinstance(value, str):
            return value.strip().lower()
        
    def handle(self, event_name, data, kwargs):
        app = self.fetch_value(data, "app")
        title = self.fetch_value(data, "title")
        content = self.fetch_value(data, "content")
        
        self.log(f"[NotificationHandlerTask] app: {app} title: {title} content: {content}")

        spreadsheet_id = self.args["money_speadsheet_id"]
        
        base_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_file = os.path.join(base_dir, "google-credentials.json")

        sheets_client = GoogleSheetsClient(
            spreadsheet_id= spreadsheet_id,
            credentials_file= credentials_file,
            sheet_range="All!A:G"  # The tab named ALL, columns A through E
        )

        processor = PushNotificationProcessor(sheets_client)

        processor.process_push_notification(
            app_name=app,
            title=title,
            description=content
        )




