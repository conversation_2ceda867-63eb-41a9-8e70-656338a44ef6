# CSV Migration Command - Complete Guide

## Overview

The CSV migration command in `src/run.py` provides a comprehensive solution for migrating historical transaction data from `expenses.csv` to Google Sheets with improved parsing logic and hierarchical categorization. This addresses the parsing errors identified in our validation (like the 890,079 COP → 33,450 COP issue) and applies the new categorization system to all historical transactions.

## Command Syntax

```bash
python src/run.py --migrate-csv <CSV_FILE> [OPTIONS]
```

## Available Options

### Core Migration Options
- `--migrate-csv CSV_FILE`: Specify the CSV file to migrate
- `--dry-run`: Preview changes without writing to Google Sheets
- `--sheet-name NAME`: Custom name for the new sheet tab (default: "Migrated YYYY-MM-DD")

### Configuration Options
- `--spreadsheet-id ID`: Google Sheets spreadsheet ID (default: configured spreadsheet)
- `--credentials PATH`: Path to Google credentials JSON file (default: google-credentials.json)
- `--verbose, -v`: Enable verbose logging for detailed output

### Other Commands
- `--sample`: Process a sample transaction for testing
- `--help`: Show help message and examples

## Usage Examples

### 1. Dry Run Migration (Recommended First Step)
```bash
python src/run.py --migrate-csv expenses.csv --dry-run
```
**Purpose**: Preview all changes without modifying Google Sheets
**Output**: Comprehensive report showing corrections and categorizations

### 2. Full Migration with Default Settings
```bash
python src/run.py --migrate-csv expenses.csv
```
**Purpose**: Migrate all data to a new sheet tab named "Migrated 2025-06-19"
**Result**: Creates new sheet with corrected data and categorization

### 3. Custom Sheet Name Migration
```bash
python src/run.py --migrate-csv expenses.csv --sheet-name "Fixed Historical Data"
```
**Purpose**: Migrate data to a custom-named sheet tab
**Result**: Creates sheet tab with specified name

### 4. Verbose Migration with Detailed Logging
```bash
python src/run.py --migrate-csv expenses.csv --dry-run --verbose
```
**Purpose**: Get detailed logging information during migration
**Output**: Step-by-step processing information

### 5. Migration to Different Spreadsheet
```bash
python src/run.py --migrate-csv expenses.csv --spreadsheet-id "your-sheet-id"
```
**Purpose**: Migrate data to a different Google Sheets document
**Requirement**: Ensure credentials have access to the target spreadsheet

## Migration Process

### 1. Data Reading and Parsing
- **Reads CSV**: Loads all transaction records from the specified CSV file
- **Enhanced Parsing**: Re-processes each transaction using improved `ExpenseInfo.parse()` method
- **Smart Filtering**: Identifies and filters promotional/non-expense messages

### 2. Amount Correction
- **Improved Regex**: Uses enhanced patterns to extract actual monetary amounts
- **Context Awareness**: Prioritizes amounts in monetary contexts ("por X", "$X")
- **Date/Time Filtering**: Excludes dates, times, and reference numbers from amount extraction

### 3. Hierarchical Categorization
- **6 Main Categories**: Ingresos, Gastos Esenciales, Gastos Discrecionales, Pago de Deudas, Ahorros, Inversiones
- **30+ Subcategories**: Detailed classification based on merchant patterns
- **Pattern Recognition**: Smart matching for known merchants and transaction types

### 4. Currency Conversion
- **Preserves Exchange Rates**: Uses original exchange rates from CSV
- **Recalculates Amounts**: Applies correct amounts to currency conversion
- **Maintains Precision**: Preserves decimal precision for USD amounts

### 5. Google Sheets Integration
- **New Sheet Creation**: Creates a new tab with timestamp or custom name
- **Full Column Structure**: Date, Exchange Rate, COP, USD, App, Description, Categoría Principal, Subcategoría
- **Batch Processing**: Efficient bulk update for large datasets
- **Header Row**: Includes proper column headers

## Migration Report

The migration generates a comprehensive report showing:

### Statistics Summary
```
📊 MIGRATION STATISTICS:
   Total rows processed: 386
   Successful migrations: 333
   Filtered messages: 53
   Parsing errors: 0
   Amount corrections: 249
   Success rate: 100.0%
```

### Categorization Breakdown
```
📂 CATEGORIZATION BREAKDOWN:
   Gastos Discrecionales: 227 transactions (68.2%)
   Gastos Esenciales: 91 transactions (27.3%)
   Ahorros: 9 transactions (2.7%)
   Pago de Deudas: 5 transactions (1.5%)
   Ingresos: 1 transactions (0.3%)
```

### Significant Corrections
```
💰 SIGNIFICANT AMOUNT CORRECTIONS:
   Row 35: 899,979 → 46,762 COP (Difference: 853,217 COP)
   Row 44: 85,784 → 6,273,150 COP (Difference: 6,187,366 COP)
   Row 49: 899,979 → 99,900 COP (Difference: 800,079 COP)
```

## Error Handling

### Graceful Error Management
- **Empty Rows**: Skips empty content gracefully
- **Malformed Data**: Handles invalid amounts and dates
- **API Errors**: Provides clear error messages for Google Sheets issues
- **File Errors**: Validates CSV file existence and format

### Logging Levels
- **INFO**: General progress and success messages
- **WARNING**: Non-critical issues (empty rows, filtered messages)
- **ERROR**: Critical errors that prevent processing
- **DEBUG**: Detailed processing information (with --verbose)

## Data Validation

### Amount Correction Detection
- **Threshold**: Flags corrections > 1,000 COP difference
- **Comparison**: Original CSV amount vs. corrected parsed amount
- **Reporting**: Lists significant corrections with details

### Message Filtering Validation
- **Promotional Messages**: Credit card offers, fitness promotions
- **System Notifications**: Exchange rate updates, login alerts
- **Investment Alerts**: Margin calls, liquidation warnings
- **Marketing Content**: Discount offers, cashback promotions

## Google Sheets Structure

### Column Layout (A:I)
| Column | Content | Example |
|--------|---------|---------|
| A | Date | 2025-06-19 12:20:23 |
| B | Exchange Rate | 4093.393338 |
| C | COP Amount | 33450 |
| D | USD Amount | 8.********* |
| E | App | com.google.android.apps.messaging |
| F | Description | scotiabank colpatria: realizaste transaccion... |
| G | Categoría Principal | Gastos Esenciales |
| H | Subcategoría | Mercado |

### Sheet Tab Naming
- **Default**: "Migrated YYYY-MM-DD" (e.g., "Migrated 2025-06-19")
- **Custom**: User-specified name via `--sheet-name` option
- **Conflict Handling**: Skips creation if sheet already exists

## Prerequisites

### Required Files
- `expenses.csv`: Source data file with transaction records
- `google-credentials.json`: Google Sheets API credentials
- Python environment with required dependencies

### Required Permissions
- Google Sheets API access
- Read/write permissions on target spreadsheet
- Ability to create new sheet tabs

## Best Practices

### 1. Always Start with Dry Run
```bash
python src/run.py --migrate-csv expenses.csv --dry-run
```
Review the report before actual migration.

### 2. Backup Original Data
Ensure you have a backup of your original CSV and Google Sheets data.

### 3. Use Verbose Mode for Troubleshooting
```bash
python src/run.py --migrate-csv expenses.csv --dry-run --verbose
```
Get detailed information if issues occur.

### 4. Custom Sheet Names for Organization
```bash
python src/run.py --migrate-csv expenses.csv --sheet-name "Historical Data Fixed"
```
Use descriptive names for easy identification.

### 5. Validate Results
After migration, spot-check a few transactions to ensure accuracy.

## Troubleshooting

### Common Issues

**CSV File Not Found**
```
Error: CSV file not found: expenses.csv
Solution: Ensure the CSV file exists in the current directory
```

**Google Sheets API Error**
```
Error: Error creating sheet tab 'Migrated 2025-06-19': ...
Solution: Check credentials and spreadsheet permissions
```

**Large Dataset Performance**
```
Issue: Migration takes a long time for large CSV files
Solution: Use --verbose to monitor progress, consider splitting large files
```

### Debug Steps
1. Run with `--dry-run` first
2. Enable `--verbose` logging
3. Check CSV file format and content
4. Verify Google Sheets credentials
5. Ensure spreadsheet permissions

## Migration Success Metrics

### Key Indicators
- **100% Success Rate**: All valid transactions processed
- **Zero Parsing Errors**: Enhanced regex handles all formats
- **Accurate Corrections**: Significant amount fixes identified
- **Proper Categorization**: All transactions assigned to categories
- **Complete Filtering**: Promotional messages correctly excluded

### Expected Results
- **Original Issue Fixed**: 890,079 COP → 33,450 COP corrections
- **Historical Data Cleaned**: All parsing errors resolved
- **Categorization Applied**: Full hierarchical classification
- **Ready for Analysis**: Clean, structured data for budget tracking

The migration command provides a complete solution for fixing historical data and applying the enhanced expense categorization system to all past transactions.
