#!/usr/bin/env python3
"""
Comprehensive validation system for expense categorization and parsing.
Analyzes expenses.csv and validates the entire system.
"""

import sys
import os
import csv
import re
from typing import List, Dict, Tuple, Optional
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from expense_info import ExpenseInfo
from expense_categorizer import ExpenseCategorizer

class ExpenseValidationSystem:
    def __init__(self):
        self.categorizer = ExpenseCategorizer()
        self.validation_results = []
        self.error_count = 0
        self.total_count = 0
        
    def analyze_csv_file(self, csv_path: str) -> Dict:
        """Analyze all transactions in the CSV file."""
        print("=" * 80)
        print("COMPREHENSIVE EXPENSE VALIDATION ANALYSIS")
        print("=" * 80)
        
        results = {
            'total_transactions': 0,
            'parsing_errors': [],
            'amount_mismatches': [],
            'categorization_issues': [],
            'filtered_messages': [],
            'successful_transactions': []
        }
        
        with open(csv_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row_num, row in enumerate(reader, start=2):  # Start at 2 because of header
                results['total_transactions'] += 1
                
                # Extract data from CSV
                content = row['Content']
                expected_cop = self._safe_float(row['COP'])
                expected_usd = self._safe_float(row['USD'])
                exchange_rate = self._safe_float(row['1 USD to COP'])
                
                # Analyze this transaction
                analysis = self._analyze_transaction(
                    content, expected_cop, expected_usd, exchange_rate, row_num
                )
                
                # Categorize the result
                if analysis['status'] == 'parsing_error':
                    results['parsing_errors'].append(analysis)
                elif analysis['status'] == 'amount_mismatch':
                    results['amount_mismatches'].append(analysis)
                elif analysis['status'] == 'filtered':
                    results['filtered_messages'].append(analysis)
                else:
                    results['successful_transactions'].append(analysis)
        
        return results
    
    def _analyze_transaction(self, content: str, expected_cop: float, 
                           expected_usd: float, exchange_rate: float, row_num: int) -> Dict:
        """Analyze a single transaction."""
        
        # Try to parse the transaction
        parsed = ExpenseInfo.parse(content)
        
        if parsed is None:
            # Check if this should have been filtered
            if self._should_be_filtered(content):
                return {
                    'status': 'filtered',
                    'row': row_num,
                    'content': content,
                    'reason': 'Correctly filtered promotional/non-expense message'
                }
            else:
                return {
                    'status': 'parsing_error',
                    'row': row_num,
                    'content': content,
                    'expected_cop': expected_cop,
                    'expected_usd': expected_usd,
                    'reason': 'Failed to parse valid transaction'
                }
        
        # Parse successful - check amounts
        try:
            parsed_amount = float(parsed.amount) if parsed.amount else 0.0
        except ValueError:
            return {
                'status': 'parsing_error',
                'row': row_num,
                'content': content,
                'expected_cop': expected_cop,
                'expected_usd': expected_usd,
                'reason': f'Invalid amount parsed: "{parsed.amount}"'
            }
        
        # Calculate what the amounts should be
        if parsed.currency == "COP":
            calculated_cop = parsed_amount
            calculated_usd = round(parsed_amount / exchange_rate, 4) if exchange_rate > 0 else 0
        elif parsed.currency == "USD":
            calculated_usd = parsed_amount
            calculated_cop = round(parsed_amount * exchange_rate) if exchange_rate > 0 else 0
        else:
            # Default to COP
            calculated_cop = parsed_amount
            calculated_usd = round(parsed_amount / exchange_rate, 4) if exchange_rate > 0 else 0
        
        # Check for amount mismatches
        cop_mismatch = abs(calculated_cop - expected_cop) > 1 if expected_cop > 0 else False
        usd_mismatch = abs(calculated_usd - expected_usd) > 0.01 if expected_usd > 0 else False
        
        if cop_mismatch or usd_mismatch:
            return {
                'status': 'amount_mismatch',
                'row': row_num,
                'content': content,
                'parsed_place': parsed.place,
                'parsed_amount': parsed.amount,
                'parsed_currency': parsed.currency,
                'calculated_cop': calculated_cop,
                'calculated_usd': calculated_usd,
                'expected_cop': expected_cop,
                'expected_usd': expected_usd,
                'cop_mismatch': cop_mismatch,
                'usd_mismatch': usd_mismatch
            }
        
        # Get categorization
        main_cat, sub_cat = self.categorizer.categorize_expense(
            parsed.place, parsed.amount, content
        )
        
        return {
            'status': 'success',
            'row': row_num,
            'content': content,
            'parsed_place': parsed.place,
            'parsed_amount': parsed.amount,
            'parsed_currency': parsed.currency,
            'main_category': main_cat,
            'subcategory': sub_cat,
            'calculated_cop': calculated_cop,
            'calculated_usd': calculated_usd
        }
    
    def _should_be_filtered(self, content: str) -> bool:
        """Check if a message should be filtered out."""
        content_lower = content.lower()
        
        # Promotional/marketing patterns
        promotional_patterns = [
            r'descuento', r'promocion', r'oferta', r'cashback', r'dto',
            r'aprovecha', r'hasta \d+%', r'solicita', r'pidela',
            r'conoce mas', r'inscribete', r'aplican t[y&]c',
            r'https?://', r'bit\.ly', r'www\.',
            r'tasa de recarga', r'conversion.*usd.*cop',
            r'para conectarte', r'no necesitas instalar',
            r'codigo.*restablecimiento', r'login.*new.*ip',
            r'margin is low', r'liquidation', r'forced liquidated'
        ]
        
        for pattern in promotional_patterns:
            if re.search(pattern, content_lower):
                return True
        
        return False
    
    def _safe_float(self, value: str) -> float:
        """Safely convert string to float."""
        try:
            if not value or value.strip() == '':
                return 0.0
            # Remove commas and convert
            cleaned = str(value).replace(',', '')
            return float(cleaned)
        except (ValueError, TypeError):
            return 0.0
    
    def print_analysis_results(self, results: Dict):
        """Print comprehensive analysis results."""
        
        print(f"\n📊 ANALYSIS SUMMARY:")
        print(f"Total transactions analyzed: {results['total_transactions']}")
        print(f"Successful transactions: {len(results['successful_transactions'])}")
        print(f"Correctly filtered messages: {len(results['filtered_messages'])}")
        print(f"Parsing errors: {len(results['parsing_errors'])}")
        print(f"Amount mismatches: {len(results['amount_mismatches'])}")
        
        # Calculate accuracy
        valid_transactions = len(results['successful_transactions']) + len(results['filtered_messages'])
        accuracy = (valid_transactions / results['total_transactions']) * 100 if results['total_transactions'] > 0 else 0
        
        print(f"\n🎯 OVERALL ACCURACY: {accuracy:.1f}%")
        
        # Show parsing errors
        if results['parsing_errors']:
            print(f"\n❌ PARSING ERRORS ({len(results['parsing_errors'])}):")
            for error in results['parsing_errors'][:5]:  # Show first 5
                print(f"  Row {error['row']}: {error['content'][:60]}...")
        
        # Show amount mismatches (the main issue)
        if results['amount_mismatches']:
            print(f"\n💰 AMOUNT MISMATCHES ({len(results['amount_mismatches'])}):")
            for mismatch in results['amount_mismatches'][:10]:  # Show first 10
                print(f"\n  Row {mismatch['row']}:")
                print(f"    Content: {mismatch['content'][:80]}...")
                print(f"    Parsed: {mismatch['parsed_amount']} {mismatch['parsed_currency']}")
                print(f"    Expected COP: {mismatch['expected_cop']:,.0f}")
                print(f"    Calculated COP: {mismatch['calculated_cop']:,.0f}")
                if mismatch['cop_mismatch']:
                    print(f"    ⚠️  COP MISMATCH: Expected {mismatch['expected_cop']:,.0f}, got {mismatch['calculated_cop']:,.0f}")
        
        # Show successful categorizations
        if results['successful_transactions']:
            print(f"\n✅ SUCCESSFUL CATEGORIZATIONS (showing first 5):")
            for success in results['successful_transactions'][:5]:
                print(f"  {success['parsed_place']} → {success['main_category']} → {success['subcategory']}")
    
    def create_test_cases(self) -> List[Dict]:
        """Create 20 comprehensive test cases covering all scenarios."""
        
        test_cases = [
            # 1. Basic COP transaction
            {
                'input': 'scotiabank colpatria: realizaste transaccion en rappi*rappi colombia por 33,450 con tu tarjeta visa platinum 2025/06/19 12:20:15',
                'expected_amount': '33450',
                'expected_currency': 'COP',
                'expected_place': 'Rappi Rappi Colombia',
                'expected_main_cat': 'Gastos Esenciales',
                'expected_sub_cat': 'Mercado'
            },
            
            # 2. USD transaction
            {
                'input': 'por $15.99 usdc en netflix subscription con tu tarjeta virtual',
                'expected_amount': '15.99',
                'expected_currency': 'COP',  # Note: USDC gets parsed as COP in current logic
                'expected_place': 'Netflix Subscription',
                'expected_main_cat': 'Gastos Discrecionales',
                'expected_sub_cat': 'Subs DFY'
            },
            
            # 3. Large amount with commas
            {
                'input': 'bancolombia: transferiste $1,084,478 desde tu cuenta *9534',
                'expected_amount': '1084478',
                'expected_currency': 'COP',
                'expected_place': 'General',
                'expected_main_cat': 'Gastos Discrecionales',
                'expected_sub_cat': 'Otros'
            },
            
            # 4. Uber transaction
            {
                'input': 'scotiabank colpatria: realizaste transaccion en uber rides por 12,067',
                'expected_amount': '12067',
                'expected_currency': 'COP',
                'expected_place': 'Uber Rides',
                'expected_main_cat': 'Gastos Discrecionales',
                'expected_sub_cat': 'Uber'
            },
            
            # 5. Gym transaction
            {
                'input': 'bodytech mensualidad por $89,900 con tu tarjeta',
                'expected_amount': '89900',
                'expected_currency': 'COP',
                'expected_place': 'Bodytech',
                'expected_main_cat': 'Gastos Esenciales',
                'expected_sub_cat': 'Gym'
            },
            
            # 6. Promotional message (should be filtered)
            {
                'input': 'daniel aprovecha descuentos hasta 50% en farmacia pasteur',
                'expected_filtered': True
            },
            
            # 7. Exchange rate notification (should be filtered)
            {
                'input': 'la tasa de recarga está en $4.135 cop y la conversión de usd a cop está en $4.035 cop',
                'expected_filtered': True
            },
            
            # 8. Gas station transaction
            {
                'input': 'realizaste transaccion en terpel por 85,000 con tu tarjeta',
                'expected_amount': '85000',
                'expected_currency': 'COP',
                'expected_place': 'Terpel',
                'expected_main_cat': 'Gastos Discrecionales',
                'expected_sub_cat': 'Carro Gasolina'
            },
            
            # 9. Decimal amount
            {
                'input': 'por $12.50 usdc en coffee shop con tu tarjeta virtual',
                'expected_amount': '12.50',
                'expected_currency': 'COP',
                'expected_place': 'Coffee Shop',
                'expected_main_cat': 'Gastos Discrecionales',
                'expected_sub_cat': 'Otros'
            },
            
            # 10. Netflix specific
            {
                'input': 'scotiabank colpatria: realizaste transaccion en dlo*google youtubeprem por 41,900',
                'expected_amount': '41900',
                'expected_currency': 'COP',
                'expected_place': 'Dlo Google Youtubeprem',
                'expected_main_cat': 'Gastos Discrecionales',
                'expected_sub_cat': 'Subs DFY'
            },
            
            # 11-20: Additional test cases covering edge cases
            {
                'input': 'compra por $120,000 cop en supermercado xyz',
                'expected_amount': '120000',
                'expected_currency': 'COP',
                'expected_place': 'General',
                'expected_main_cat': 'Gastos Esenciales',
                'expected_sub_cat': 'Mercado'
            },
            
            # Continue with more test cases...
        ]
        
        return test_cases
    
    def run_test_cases(self, test_cases: List[Dict]) -> Dict:
        """Run all test cases and return results."""
        
        print("\n" + "=" * 80)
        print("RUNNING COMPREHENSIVE TEST CASES")
        print("=" * 80)
        
        results = {
            'total_tests': len(test_cases),
            'passed': 0,
            'failed': 0,
            'failures': []
        }
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\nTest {i}: {test_case['input'][:60]}...")
            
            if test_case.get('expected_filtered'):
                # Test filtering
                parsed = ExpenseInfo.parse(test_case['input'])
                if parsed is None:
                    print(f"  ✅ PASS: Correctly filtered")
                    results['passed'] += 1
                else:
                    print(f"  ❌ FAIL: Should have been filtered but was parsed")
                    results['failed'] += 1
                    results['failures'].append({
                        'test': i,
                        'input': test_case['input'],
                        'error': 'Should have been filtered'
                    })
            else:
                # Test parsing and categorization
                parsed = ExpenseInfo.parse(test_case['input'])
                
                if parsed is None:
                    print(f"  ❌ FAIL: Failed to parse")
                    results['failed'] += 1
                    results['failures'].append({
                        'test': i,
                        'input': test_case['input'],
                        'error': 'Failed to parse'
                    })
                    continue
                
                # Check parsing results
                parsing_correct = (
                    parsed.amount == test_case['expected_amount'] and
                    parsed.currency == test_case['expected_currency'] and
                    parsed.place == test_case['expected_place']
                )
                
                # Check categorization
                main_cat, sub_cat = self.categorizer.categorize_expense(
                    parsed.place, parsed.amount, test_case['input']
                )
                
                categorization_correct = (
                    main_cat == test_case['expected_main_cat'] and
                    sub_cat == test_case['expected_sub_cat']
                )
                
                if parsing_correct and categorization_correct:
                    print(f"  ✅ PASS: {parsed.amount} {parsed.currency} → {main_cat} → {sub_cat}")
                    results['passed'] += 1
                else:
                    print(f"  ❌ FAIL:")
                    if not parsing_correct:
                        print(f"    Parsing: Expected {test_case['expected_amount']} {test_case['expected_currency']}, got {parsed.amount} {parsed.currency}")
                    if not categorization_correct:
                        print(f"    Category: Expected {test_case['expected_main_cat']} → {test_case['expected_sub_cat']}, got {main_cat} → {sub_cat}")
                    
                    results['failed'] += 1
                    results['failures'].append({
                        'test': i,
                        'input': test_case['input'],
                        'expected': test_case,
                        'actual': {
                            'amount': parsed.amount,
                            'currency': parsed.currency,
                            'place': parsed.place,
                            'main_cat': main_cat,
                            'sub_cat': sub_cat
                        }
                    })
        
        return results

def main():
    """Main validation function."""
    validator = ExpenseValidationSystem()
    
    # 1. Analyze CSV file
    print("Step 1: Analyzing expenses.csv file...")
    csv_results = validator.analyze_csv_file('expenses.csv')
    validator.print_analysis_results(csv_results)
    
    # 2. Create and run test cases
    print("\nStep 2: Running comprehensive test cases...")
    test_cases = validator.create_test_cases()
    test_results = validator.run_test_cases(test_cases)
    
    # 3. Print final summary
    print("\n" + "=" * 80)
    print("FINAL VALIDATION SUMMARY")
    print("=" * 80)
    
    csv_accuracy = ((len(csv_results['successful_transactions']) + len(csv_results['filtered_messages'])) / csv_results['total_transactions']) * 100
    test_accuracy = (test_results['passed'] / test_results['total_tests']) * 100
    
    print(f"CSV Analysis Accuracy: {csv_accuracy:.1f}%")
    print(f"Test Cases Accuracy: {test_accuracy:.1f}%")
    print(f"Major Issues Found: {len(csv_results['amount_mismatches'])} amount mismatches")
    
    if csv_results['amount_mismatches']:
        print(f"\n🔧 RECOMMENDED FIXES:")
        print("1. Fix amount parsing regex to avoid capturing dates/reference numbers")
        print("2. Improve currency detection logic")
        print("3. Add validation for parsed amounts vs expected ranges")

if __name__ == "__main__":
    main()
